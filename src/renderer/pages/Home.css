/* Main Layout - 25% / 75% Split - Full Width with Navbar */
.sales-page {
  height: calc(100vh - 52px); /* Navbar height'ı çıkar */
  width: 100vw;
  margin: 0;
  padding: 0;
  position: fixed;
  top: 52px; /* Navbar height'ı kadar a<PERSON> b<PERSON> */
  left: 0;
}

.main-layout {
  display: flex;
  height: calc(100vh - 52px); /* Navbar height'ı çıkar */
  width: 100vw;
  margin: 0;
  padding: 0;
}

/* Left Panel - Sales Screen (25%) */
.sales-panel {
  width: 50%;
  min-width: 300px;
  height: calc(100vh - 52px); /* Navbar height'ı çıkar */
  overflow-y: auto;
  background: linear-gradient(135deg, #f8f9fb 0%, #f1f3f7 100%);
  border-right: 2px solid #e5e7eb;
  padding: 1.5rem;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

/* Right Panel - Future Content (75%) */
.content-panel {
  width: 50%;
  height: calc(100vh - 52px); /* Navbar height'ı çıkar */
  overflow-y: auto;
  padding: 0 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fb 100%);
}

/* Panel Headers */

/* Tab Section Styling */
.tab-section {
  margin-bottom: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
  z-index: 1;
}

.tab-section .tabs {
  margin-bottom: 0 !important;
  border-bottom: 1px solid #e5e7eb;
}

.tab-section .tabs ul {
  border-bottom: none !important;
}

.tab-section .tabs li .tab-button {
  border: none;
  background: none;
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem; /* DAHA FAZLA BOŞLUK */
  border-bottom: 2px solid transparent !important;
  padding: 1.25rem 1.5rem !important; /* BÜYÜK PADDING */
  font-size: 1.3rem !important; /* BÜYÜK FONT - INPUT İLE AYNI */
  font-weight: 600 !important; /* DAHA KALIN */
  color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

/* TAB ikonları büyütme */
.tab-section .tabs li .tab-button .icon {
  font-size: 1.4rem !important; /* BÜYÜK İKON */
}

.tab-section .tabs li .tab-button .icon i {
  font-size: 1.4rem !important; /* BÜYÜK İKON İÇERİĞİ */
}

.tab-section .tabs li.is-active .tab-button {
  color: #1e3a8a !important;
  border-bottom-color: #1e3a8a !important;
  background: rgba(30, 58, 138, 0.05) !important;
}

.tab-section .tabs li .tab-button:hover {
  color: #1e3a8a !important;
  background: rgba(30, 58, 138, 0.03) !important;
}

.tab-section .tabs li .tab-button:focus {
  outline: 2px solid #1e3a8a;
  outline-offset: 2px;
}

.tab-content {
  padding: 1.5rem;
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
}

.search-tab-content {
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
  z-index: 10;
}

.search-tab-content .field {
  margin-bottom: 0.75rem;
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
}

/* Category Grid Styling */
.category-tab-content {
  padding: 1rem;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 400px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
}

.category-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.category-image-placeholder {
  width: 50px;
  height: 50px;
  background: #e5e7eb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1.25rem;
  border: 2px dashed #d1d5db;
}

.category-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  text-align: center;
}

/* Sales Table Section */
.sales-table-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

/* Content Area */

/* Responsive Design */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .sales-panel {
    width: 100%;
    height: 50vh;
    min-width: unset;
    padding: 1rem;
  }

  .content-panel {
    width: 100%;
    height: 50vh;
    padding: 1rem;
  }

  /* Mobile total amount styling */
  .total-amount-container {
    height: 40px;
  }

  .total-label {
    font-size: 0.9rem;
    min-width: 100px;
    padding: 0 1rem;
  }

  .total-amount {
    font-size: 1rem;
    min-width: 120px;
    padding: 0 1rem;
  }

  /* Mobile search input styling - GÜÇLÜ OVERRIDE */
  .autocomplete-container .input,
  .autocomplete-container .input.is-large,
  .control.has-icons-left .input {
    padding: 0.75rem 1rem 0.75rem 5rem !important;
    padding-left: 5rem !important;
    font-size: 1.1rem !important; /* BÜYÜK MOBİL FONT */
    height: 55px !important; /* BÜYÜK MOBİL YÜKSEKLİK */
  }

  /* Mobile Ekle butonu */
  .button.is-primary.is-large {
    height: 55px !important; /* MOBİL INPUT İLE AYNI YÜKSEKLİK */
    font-size: 1.1rem !important; /* MOBİL INPUT İLE AYNI FONT */
    padding: 0.75rem 1.25rem !important;
  }

  /* Mobile TAB'lar */
  .tab-section .tabs li .tab-button {
    padding: 1rem 1.25rem !important;
    font-size: 1.1rem !important; /* MOBİL INPUT İLE AYNI FONT */
  }

  .tab-section .tabs li .tab-button .icon,
  .tab-section .tabs li .tab-button .icon i {
    font-size: 1.2rem !important; /* MOBİL BÜYÜK İKON */
  }

  .autocomplete-container .icon.is-left,
  .control.has-icons-left .icon.is-left {
    left: 1.25rem !important;
    font-size: 1.3rem !important; /* BÜYÜK MOBİL İKON */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 15 !important;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .total-amount-container {
    height: 36px;
  }

  .total-label {
    font-size: 0.8rem;
    min-width: 80px;
    padding: 0 0.75rem;
  }

  .total-amount {
    font-size: 0.9rem;
    min-width: 100px;
    padding: 0 0.75rem;
  }

  /* Extra small search input styling - GÜÇLÜ OVERRIDE */
  .autocomplete-container .input,
  .autocomplete-container .input.is-large,
  .control.has-icons-left .input {
    padding: 0.6rem 0.75rem 0.6rem 4.5rem !important;
    padding-left: 4.5rem !important;
    font-size: 1rem !important; /* BÜYÜK EXTRA SMALL FONT */
    height: 50px !important; /* BÜYÜK EXTRA SMALL YÜKSEKLİK */
  }

  /* Extra Small Ekle butonu */
  .button.is-primary.is-large {
    height: 50px !important; /* EXTRA SMALL INPUT İLE AYNI YÜKSEKLİK */
    font-size: 1rem !important; /* EXTRA SMALL INPUT İLE AYNI FONT */
    padding: 0.6rem 1rem !important;
  }

  /* Extra Small TAB'lar */
  .tab-section .tabs li .tab-button {
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important; /* EXTRA SMALL INPUT İLE AYNI FONT */
  }

  .tab-section .tabs li .tab-button .icon,
  .tab-section .tabs li .tab-button .icon i {
    font-size: 1.1rem !important; /* EXTRA SMALL BÜYÜK İKON */
  }

  .autocomplete-container .icon.is-left,
  .control.has-icons-left .icon.is-left {
    left: 1rem !important;
    font-size: 1.2rem !important; /* BÜYÜK EXTRA SMALL İKON */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 15 !important;
  }
}

/* Decimal alignment for currency values */
.decimal-aligned {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  text-align: right;
  display: inline-block;
  min-width: 100px;
  letter-spacing: 0.5px;
}

/* Table column alignment */
.table td.has-text-right {
  text-align: right;
  vertical-align: middle;
}

/* Table header alignment */
.table th.has-text-right {
  text-align: right;
}

/* Table cell vertical alignment */
.table td {
  vertical-align: middle !important;
}

.table th {
  vertical-align: middle !important;
}

/* Sales table column width optimization */
.sales-table {
  table-layout: fixed !important;
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.sales-table th,
.sales-table td {
  padding: 4px 6px !important;
  margin: 0 !important;
  border: 1px solid #e5e7eb !important;
  line-height: 1.2 !important;
  font-size: 0.9rem !important;
}

.sales-table th {
  padding: 6px 8px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  background-color: #f8f9fa !important;
}

.sales-table tbody tr {
  height: auto !important;
  min-height: 32px !important;
}

.sales-table .col-sequence {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  text-align: center !important;
}

.sales-table .col-product {
  width: auto !important;
  min-width: 200px !important;
}

.sales-table .col-unit {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: center !important;
}

.sales-table .col-price {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.sales-table .col-quantity {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.sales-table .col-total {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.sales-table .col-actions {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  text-align: center !important;
}

/* Table cell content alignment and width enforcement */
.sales-table tbody td:nth-child(1) {
  width: 60px !important;
  text-align: center !important;
}

.sales-table tbody td:nth-child(2) {
  width: auto !important;
  min-width: 200px !important;
}

.sales-table tbody td:nth-child(3) {
  width: 80px !important;
  text-align: center !important;
}

.sales-table tbody td:nth-child(4) {
  width: 120px !important;
  text-align: right !important;
}

.sales-table tbody td:nth-child(5) {
  width: 120px !important;
  text-align: center !important;
}

.sales-table tbody td:nth-child(6) {
  width: 120px !important;
  text-align: right !important;
}

.sales-table tbody td:nth-child(7) {
  width: 100px !important;
  text-align: center !important;
}

/* Compact quantity input styling */

.sales-table .quantity-btn {
  width: 18px !important;
  height: 18px !important;
  min-height: 18px !important;
  padding: 0 !important;
  margin: 0 !important;
  border: 1px solid #d1d5db !important;
  font-size: 10px !important;
  font-weight: bold !important;
  border-radius: 2px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  transition: all 0.2s ease !important;
}

/* Increment button (+ button) - Green */
.quantity-btn.increment,
.sales-table .quantity-btn.increment {
  background: #10b981 !important;
  color: white !important;
  border-color: #059669 !important;
}

.quantity-btn.increment:hover,
.sales-table .quantity-btn.increment:hover {
  background: #059669 !important;
  border-color: #047857 !important;
  transform: scale(1.05) !important;
}

/* Decrement button (- button) - Red */
.quantity-btn.decrement,
.sales-table .quantity-btn.decrement {
  background: #ef4444 !important;
  color: white !important;
  border-color: #dc2626 !important;
}

.quantity-btn.decrement:hover,
.sales-table .quantity-btn.decrement:hover {
  background: #dc2626 !important;
  border-color: #b91c1c !important;
  transform: scale(1.05) !important;
}

/* Override Bulma button styles for quantity buttons */
.button.quantity-btn {
  border-width: 1px !important;
  font-weight: bold !important;
  transition: all 0.2s ease !important;
}

.button.quantity-btn .icon {
  color: inherit !important;
}

/* Quantity controls vertical alignment */
.quantity-controls {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.quantity-controls .control {
  display: flex !important;
  align-items: center !important;
}

.quantity-controls .button {
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.quantity-controls .input {
  height: 32px !important;
  width: 70px !important;
  text-align: center !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.9rem !important;
  padding: 4px 6px !important;
}

/* Compact delete button */

/* Compact table container */
.table-container {
  padding: 0 !important;
  margin: 0 !important;
  overflow-x: auto !important;
}

/* Remove extra spacing from Bulma table */
.sales-table.table {
  margin-bottom: 0 !important;
}

/* Compact table wrapper */

/* Total Amount Container Styling - BÜYÜK BOYUT */
.total-amount-container,
.tags.has-addons.total-amount-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 70px !important; /* DAHA BÜYÜK */
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
  transform: scale(1.1) !important; /* %10 BÜYÜTME */
  width: 49% !important; /* İKİ BUTON EŞİT GENİŞLİKTE */
}

.total-label,
.tag.total-label {
  background: linear-gradient(135deg, #800020 0%, #8b1538 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  font-size: 1.5rem !important; /* DAHA BÜYÜK FONT */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 2rem !important; /* DAHA FAZLA PADDING */
  border-radius: 12px 0 0 12px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  min-width: 160px !important; /* DAHA GENİŞ */
  height: 100% !important;
  vertical-align: middle !important;
  border: none !important;
  flex: 1;
}

.total-amount,
.tag.total-amount {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
  color: white !important;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-weight: 800 !important;
  font-size: 1.6rem !important; /* DAHA BÜYÜK FONT */
  letter-spacing: 0.8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 2rem !important; /* DAHA FAZLA PADDING */
  border-radius: 0 12px 12px 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  min-width: 180px !important; /* DAHA GENİŞ */
  height: 100% !important;
  transition: all 0.3s ease !important;
  vertical-align: middle !important;
  border: none !important;
  flex: 1;
}

/* Hover effects for total amount container */
.total-amount-container:hover .total-label {
  background: linear-gradient(135deg, #8b1538 0%, #a01b42 100%) !important;
  transform: scale(1.02);
}

.total-amount-container:hover .total-amount {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
  transform: scale(1.02);
}

.total-amount-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

/* Tag alignment for decimal values */
.tag.decimal-aligned {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  letter-spacing: 0.5px;
  text-align: right;
  justify-content: flex-end;
}

/* Autocomplete Styles */
.autocomplete-container {
  position: relative !important;
  width: 100% !important;
  max-width: 100% !important;
  z-index: 1000 !important;
}

.autocomplete-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border: 2px solid #1e3a8a;
  border-radius: 8px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

.autocomplete-item {
  padding: 1.25rem 1.5rem;
  cursor: pointer;
  border: none;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.3s ease;
  font-size: 1rem;
  background: #ffffff;
  margin: 0;
  position: relative;
  border-radius: 0;
  width: 100%;
  box-sizing: border-box;
}

.autocomplete-item:hover,
.autocomplete-item.is-selected {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.06) 0%, rgba(128, 0, 32, 0.06) 100%);
  border-left: 4px solid #1e3a8a;
  border-bottom: 1px solid #f1f5f9;
  padding-left: calc(1.5rem - 4px);
  transform: translateX(0);
  box-shadow: none;
}

.autocomplete-item:last-child {
  border-bottom: none;
  border-radius: 0 0 6px 6px;
}

.autocomplete-item:first-child {
  border-radius: 6px 6px 0 0;
}

.autocomplete-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.autocomplete-item-name {
  font-size: 1.05rem;
  color: #1f2937;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.25rem;
  width: 100%;
  word-wrap: break-word;
}

.autocomplete-item-details {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start;
}

.autocomplete-item-details .tag {
  font-size: 0.8rem;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.25rem 0.6rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.autocomplete-item-details .tag.is-primary {
  background: linear-gradient(135deg, rgba(128, 0, 32, 0.1) 0%, rgba(139, 21, 56, 0.1) 100%);
  color: #800020;
  border-color: rgba(128, 0, 32, 0.2);
}

/* Prevent dropdown from affecting layout */
.autocomplete-dropdown {
  position: absolute !important;
  z-index: 9999 !important;
}

/* Search section adjustments */

/* Input field styling with left icon - GÜÇLÜ OVERRIDE */
.autocomplete-container .input,
.autocomplete-container .input.is-large,
.control.has-icons-left .input {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.3s ease !important;
  font-size: 1.3rem !important; /* BÜYÜK FONT */
  padding: 1rem 1.25rem 1rem 5.5rem !important; /* BÜYÜK PADDING */
  padding-left: 5.5rem !important;
  height: 60px !important; /* BÜYÜK YÜKSEKLİK */
}

/* Ekle butonu input ile aynı yükseklikte */
.button.is-primary.is-large {
  height: 60px !important; /* INPUT İLE AYNI YÜKSEKLİK */
  font-size: 1.3rem !important; /* INPUT İLE AYNI FONT */
  padding: 1rem 1.5rem !important;
  border-radius: 8px !important;
}

.autocomplete-container .input:focus {
  border-color: #1e3a8a;
  border-radius: 8px;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
  outline: none;
}

/* When dropdown is visible, keep input separate */
.autocomplete-container.has-dropdown .input {
  border-radius: 8px;
  border-color: #1e3a8a;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Search icon positioning - GÜÇLÜ OVERRIDE */
.autocomplete-container .icon.is-left,
.control.has-icons-left .icon.is-left,
.field .control .icon.is-left {
  position: absolute !important;
  left: 1.75rem !important; /* DAHA SOL */
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #1e3a8a !important;
  pointer-events: none !important;
  z-index: 15 !important; /* DAHA YÜKSEK Z-INDEX */
  font-size: 1.5rem !important; /* ÇOK BÜYÜK İKON */
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: auto !important;
  height: auto !important;
}

/* Icon visibility on all input states */
.autocomplete-container .input:hover + .icon.is-left,
.autocomplete-container .input:focus + .icon.is-left,
.autocomplete-container .input:active + .icon.is-left {
  color: #1e3a8a !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 10 !important;
}

/* Ensure icon is always visible in all states */
.autocomplete-container .icon.is-left i {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
  display: inline-block !important;
}

/* Override any Bulma hover states that might hide the icon */
.autocomplete-container:hover .icon.is-left,
.autocomplete-container .control:hover .icon.is-left {
  opacity: 1 !important;
  visibility: visible !important;
  color: #1e3a8a !important;
  z-index: 10 !important;
}

/* POS Payment System Styles */
.payment-header {
  background: linear-gradient(135deg, #f8f9fb 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.payment-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.summary-label {
  font-size: 1.1rem; /* BÜYÜK LABEL */
  color: #6b7280;
  font-weight: 600; /* DAHA KALIN */
}

.summary-value {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-weight: 800; /* ÇOK KALIN */
  font-size: 1.4rem; /* BÜYÜK DEĞER */
}

.summary-value.total {
  color: #1e3a8a;
}

.summary-value.discount {
  color: #f59e0b;
}

.summary-value.paid {
  color: #10b981;
}

.summary-value.remaining {
  color: #ef4444;
}

.remaining-amount-display {
  background: linear-gradient(135deg, #800020 0%, #1e3a8a 100%);
  color: white;
  padding: 0;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  height: 70px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 49% !important;
  overflow: hidden !important;
  transform: scale(1.1) !important;
}

.remaining-label {
  background: linear-gradient(135deg, #800020 0%, #8b1538 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  font-size: 1.5rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 2rem !important;
  border-radius: 12px 0 0 12px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  min-width: 160px !important;
  height: 100% !important;
  vertical-align: middle !important;
  border: none !important;
  flex: 1;
  margin: 0;
  opacity: 1;
}

.remaining-amount {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 1.6rem;
  font-weight: 700;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 1.5rem;
  height: calc(100vh - 300px);
  max-height: calc(100vh - 300px);
}

/* POS Payment Methods - Isolated */
.content-panel .payment-methods {
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
  position: relative !important;
}

.content-panel .payment-btn {
  padding: 1rem !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.content-panel .payment-btn.cash {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.credit-card {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.meal-card {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.cash-drawer {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.installment {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  color: white !important;
}

.content-panel .payment-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
}

/* Active/Selected payment button styles */
.content-panel .payment-btn.is-active,
.content-panel .payment-btn.is-selected {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  border: 3px solid #ffffff !important;
  outline: 2px solid #1e40af !important;
  outline-offset: 2px !important;
}

.content-panel .payment-btn.cash.is-active,
.content-panel .payment-btn.cash.is-selected {
  outline-color: #059669 !important;
}

.content-panel .payment-btn.credit-card.is-active,
.content-panel .payment-btn.credit-card.is-selected {
  outline-color: #1e40af !important;
}

.content-panel .payment-btn.meal-card.is-active,
.content-panel .payment-btn.meal-card.is-selected {
  outline-color: #d97706 !important;
}

.content-panel .payment-btn.cash-drawer.is-active,
.content-panel .payment-btn.cash-drawer.is-selected {
  outline-color: #dc2626 !important;
}

.content-panel .payment-btn.installment.is-active,
.content-panel .payment-btn.installment.is-selected {
  outline-color: #7c3aed !important;
}

/* POS Numeric Keypad - Isolated from simple-keyboard */
.content-panel .numeric-keypad {
  display: flex !important;
  flex-direction: column !important;
  gap: 1rem !important;
  position: relative !important;
  z-index: 1 !important;
}

.content-panel .amount-display {
  background: #1f2937 !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  border: 2px solid #374151 !important;
}

.content-panel .amount-input {
  width: 100% !important;
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
  text-align: center !important;
  outline: none !important;
}

.content-panel .amount-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.content-panel .keypad-table {
  width: 100% !important;
  border-collapse: separate !important;
  border-spacing: 2px !important;
  position: relative !important;
  padding: 0 !important;
  margin: 0 !important;
}

.content-panel .keypad-row {
  height: 90px !important;
}

.content-panel .keypad-table td {
  padding: 1px !important;
  margin: 0 !important;
  border: none !important;
  vertical-align: middle !important;
  text-align: center !important;
}

.content-panel .key-btn {
  background: #374151 !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 70px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
  padding: px !important;
  box-sizing: border-box !important;
}

.content-panel .key-btn:hover {
  background: #4b5563 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.content-panel .key-btn:active {
  transform: scale(0.95) !important;
}

.content-panel .key-btn.backspace {
  background: #ef4444 !important;
}

.content-panel .key-btn.backspace:hover {
  background: #dc2626 !important;
}

.content-panel .key-btn.clear {
  background: #f59e0b !important;
}

.content-panel .key-btn.clear:hover {
  background: #d97706 !important;
}

.content-panel .key-btn.decimal {
  background: #6b7280 !important;
  color: white !important;
  font-size: 1.3rem !important;
  font-weight: 700 !important;
}

.content-panel .key-btn.decimal:hover {
  background: #4b5563 !important;
}

/* Responsive keypad spacing - consistent across all resolutions */
@media screen and (max-width: 1366px) {
  .content-panel .keypad-table {
    border-spacing: 1px !important;
  }
  .content-panel .keypad-row {
    height: 84px !important;
  }
  .content-panel .key-btn {
    min-height: 76px !important;
    font-size: 1.05rem !important;
  }
}

@media screen and (max-width: 1024px) {
  .content-panel .keypad-table {
    border-spacing: 1px !important;
  }
  .content-panel .keypad-row {
    height: 80px !important;
  }
  .content-panel .key-btn {
    min-height: 72px !important;
    font-size: 1rem !important;
  }
}

@media screen and (max-width: 768px) {
  .content-panel .keypad-table {
    border-spacing: 1px !important;
  }
  .content-panel .keypad-row {
    height: 76px !important;
  }
  .content-panel .key-btn {
    min-height: 68px !important;
    font-size: 0.95rem !important;
  }
}

@media screen and (min-width: 1920px) {
  .content-panel .keypad-table {
    border-spacing: 3px !important;
  }
  .content-panel .keypad-row {
    height: 100px !important;
  }
  .content-panel .key-btn {
    min-height: 92px !important;
    font-size: 1.2rem !important;
  }
}

@media screen and (min-width: 2560px) {
  .content-panel .keypad-table {
    border-spacing: 4px !important;
  }
  .content-panel .keypad-row {
    height: 110px !important;
  }
  .content-panel .key-btn {
    min-height: 102px !important;
    font-size: 1.3rem !important;
  }
}

/* Sale Buttons Container */
.content-panel .sale-buttons {
  display: flex !important;
  gap: 1rem !important;
  margin-top: 0.4rem !important;
}

.content-panel .complete-sale-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.2) !important;
  flex: 2 !important;
  height: 90px;
}

.content-panel .demo-sale-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
  flex: 1 !important;
  height: 90px;
}

.content-panel .pair-device-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.2) !important;
  flex: 1 !important;
  height: 90px;
}

.content-panel .sales-list-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.2) !important;
  flex: 1 !important;
  height: 90px;
}

.content-panel .complete-sale-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3) !important;
}

.content-panel .demo-sale-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3) !important;
}

.content-panel .pair-device-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(245, 158, 11, 0.3) !important;
}

.content-panel .sales-list-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 16px rgba(139, 92, 246, 0.3) !important;
}

/* POS Quick Actions - Isolated */
.content-panel .quick-actions {
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
  position: relative !important;
}

.content-panel .action-btn {
  padding: 1rem !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  background: white !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.content-panel .action-btn.loyalty {
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

.content-panel .action-btn.premium {
  border-color: #8b5cf6 !important;
  color: #8b5cf6 !important;
}

.content-panel .action-btn.gift {
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.content-panel .action-btn.receipt {
  border-color: #6b7280 !important;
  color: #6b7280 !important;
}

.content-panel .action-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.content-panel .action-btn.loyalty:hover {
  background: #3b82f6 !important;
  color: white !important;
}

.content-panel .action-btn.premium:hover {
  background: #8b5cf6 !important;
  color: white !important;
}

.content-panel .action-btn.gift:hover {
  background: #f59e0b !important;
  color: white !important;
}

.content-panel .action-btn.receipt:hover {
  background: #6b7280 !important;
  color: white !important;
}

.content-panel .action-btn.is-warning {
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.content-panel .action-btn.is-warning:hover {
  background: #f59e0b !important;
  color: white !important;
}

.content-panel .action-btn.is-info {
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

.content-panel .action-btn.is-info:hover {
  background: #3b82f6 !important;
  color: white !important;
}

/* Smooth scrollbar for dropdown */
.autocomplete-dropdown::-webkit-scrollbar {
  width: 6px;
}

.autocomplete-dropdown::-webkit-scrollbar-track {
  background: #f1f3f7;
  border-radius: 0 3px 3px 0;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #800020 0%, #1e3a8a 100%);
  border-radius: 3px;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #8b1538 0%, #3b82f6 100%);
}

/* Partial Payments Styles */
.partial-payments {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.partial-payments h4 {
  margin: 0 0 12px 0;
  color: #334155;
  font-size: 14px;
  font-weight: 600;
}

.payments-list {
  max-height: 120px;
  overflow-y: auto;
}

.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 6px;
  font-size: 13px;
}

.payment-method {
  color: #475569;
  font-weight: 500;
  flex: 1;
}

.payment-amount {
  color: #059669;
  font-weight: 600;
  margin-right: 8px;
}

.remove-payment {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.remove-payment:hover {
  background: #dc2626;
}

.payments-total {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
  color: #1e293b;
  font-size: 14px;
}

.clear-all-payments {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 8px;
}

.clear-all-payments:hover {
  background: #d97706;
}

/* Payment Complete State */
.summary-value.remaining.complete {
  color: #059669 !important;
  font-weight: 600 !important;
}

/* Remaining Amount Button Styles */
.remaining-amount-display.clickable {
  cursor: pointer;
}

.remaining-amount-btn {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
  color: white;
  border: none;
  border-radius: 0 12px 12px 0 !important;
  padding: 0 2rem !important;
  font-size: 1.6rem;
  font-weight: 800;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.2);
  height: 100% !important;
  flex: 1;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  min-width: 180px !important;
}

.remaining-amount-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed 0%, #4f46e5 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(139, 92, 246, 0.3);
}

.remaining-amount-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
}

.remaining-amount-btn.active {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Cash Change Modal Styles */
.cash-payment-info {
  padding: 1rem 0;
}

.cash-payment-info .field {
  margin-bottom: 1.5rem;
}

.cash-payment-info .label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.cash-payment-info .tag {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 1.4rem;
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
}

.cash-payment-info .tag.is-large {
  font-size: 1.6rem;
  padding: 1rem 2rem;
}

.cash-payment-info .notification {
  margin-top: 1rem;
  border-radius: 8px;
}

.modal-card-foot .button.is-success {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 0.75rem 2rem;
}

.modal-card-foot .button.is-success:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

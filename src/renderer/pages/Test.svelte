<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { getTodaysSales } from '../utils/database.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'

  // Authentication state
  $: authState = $authStore

  // Check authentication and redirect if not authenticated
  $: if (authState && !authState.isLoading && !authState.isAuthenticated) {
    push('/login')
  }

  // Sales state
  let todaysSales = []
  let isLoadingSales = false
  let lastRefresh = null

  onMount(async () => {
    await loadTodaysSales()
  })

  async function loadTodaysSales() {
    isLoadingSales = true
    try {
      todaysSales = await getTodaysSales()
      lastRefresh = new Date()
      console.log('📊 Bugünkü satışlar yüklendi:', todaysSales)
      showSuccess(`${todaysSales.length} satış kaydı yüklendi`)
    } catch (error) {
      console.error('❌ Satışlar yüklenirken hata:', error)
      showError('Satışlar yüklenirken hata oluştu')
    } finally {
      isLoadingSales = false
    }
  }

  function formatTime(dateString) {
    const date = new Date(dateString)
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  function formatCurrency(amount) {
    return amount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    })
  }

  function formatDate(date) {
    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
</script>

<div class="sales-page">
  <div class="hero light-theme-hero">
    <div class="hero-body">
      <div class="container">
        <h1 class="title light-theme-title">
          <span class="icon">
            <i class="fas fa-chart-line"></i>
          </span>
          Satışlar
        </h1>
        <h2 class="subtitle light-theme-subtitle">
          Bugünkü satış işlemlerini görüntüleyin ve yönetin
        </h2>
      </div>
    </div>
  </div>

  <div class="section">
    <div class="container">
      <!-- Sales Controls -->
      <div class="box light-theme-box mb-5">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h3 class="title is-4 light-theme-text">
                <span class="icon">
                  <i class="fas fa-list"></i>
                </span>
                Bugünkü Satışlar
              </h3>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button
                class="button light-theme-button"
                on:click={loadTodaysSales}
                disabled={isLoadingSales}
              >
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Yenile</span>
              </button>
            </div>
            {#if lastRefresh}
              <div class="level-item">
                <small class="has-text-grey">
                  Son güncelleme: {formatDate(lastRefresh)}
                </small>
              </div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Sales Content -->
      {#if isLoadingSales}
        <div class="box light-theme-box">
          <div class="has-text-centered">
            <span class="icon is-large">
              <i class="fas fa-spinner fa-pulse fa-2x"></i>
            </span>
            <p class="mt-3">Satışlar yükleniyor...</p>
          </div>
        </div>
      {:else if todaysSales.length === 0}
        <div class="box light-theme-box">
          <div class="notification is-info is-light">
            <span class="icon">
              <i class="fas fa-info-circle"></i>
            </span>
            <span>Bugün henüz satış yapılmamış.</span>
          </div>
        </div>
      {:else}
        <!-- Sales Table -->
        <div class="box light-theme-box">
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Fiş No</th>
                  <th>Saat</th>
                  <th class="has-text-right">Tutar</th>
                  <th>Ödeme</th>
                  <th>Personel</th>
                </tr>
              </thead>
              <tbody>
                {#each todaysSales as sale (sale.id)}
                  <tr>
                    <td>
                      <span class="tag is-primary">{sale.receipt_number}</span>
                    </td>
                    <td>{formatTime(sale.created_at)}</td>
                    <td class="has-text-right">
                      <strong>{formatCurrency(sale.total_price)}</strong>
                    </td>
                    <td>
                      <span class="tag is-light">{sale.payment_methods || 'Bilinmiyor'}</span>
                    </td>
                    <td>
                      <span class="tag is-info is-light">
                        {sale.employee_full_name || sale.employee_code || 'Bilinmiyor'}
                      </span>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Sales Summary -->
        <div class="box light-theme-box">
          <h4 class="title is-5 light-theme-text">
            <span class="icon">
              <i class="fas fa-chart-bar"></i>
            </span>
            Günlük Özet
          </h4>
          <div class="level">
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Toplam Satış</p>
                <p class="title is-4">{todaysSales.length}</p>
              </div>
            </div>
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Toplam Tutar</p>
                <p class="title is-4">
                  {formatCurrency(todaysSales.reduce((sum, sale) => sum + sale.total_price, 0))}
                </p>
              </div>
            </div>
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Ortalama Satış</p>
                <p class="title is-4">
                  {formatCurrency(
                    todaysSales.reduce((sum, sale) => sum + sale.total_price, 0) /
                      todaysSales.length
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  /* Light Theme Styles */
  .light-theme-hero {
    background: linear-gradient(
      135deg,
      var(--color-light-secondary),
      var(--color-light-secondary-gradient)
    );
    color: white;
  }

  .light-theme-title {
    color: white !important;
  }

  .light-theme-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .light-theme-box {
    background-color: var(--color-light) !important;
    border: 1px solid var(--color-light-trd);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .light-theme-text {
    color: var(--color-light-text) !important;
  }

  /* Button Styles */
  .light-theme-button {
    background-color: var(--color-theme-light) !important;
    border-color: var(--color-theme-light) !important;
    color: white !important;
  }

  .light-theme-button:hover {
    background-color: #c91653 !important;
    border-color: #c91653 !important;
  }

  .light-theme-button-secondary {
    background-color: var(--color-light-secondary) !important;
    border-color: var(--color-light-secondary) !important;
    color: white !important;
  }

  .light-theme-button-secondary:hover {
    background-color: var(--color-light-secondary-gradient) !important;
    border-color: var(--color-light-secondary-gradient) !important;
  }

  .light-theme-button-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
  }

  .light-theme-button-success:hover {
    background-color: #218838 !important;
    border-color: #218838 !important;
  }

  .light-theme-button-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
  }

  .light-theme-button-warning:hover {
    background-color: #e0a800 !important;
    border-color: #e0a800 !important;
  }

  .light-theme-button-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
  }

  .light-theme-button-danger:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
  }

  .light-theme-button-purple {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    color: white !important;
  }

  .light-theme-button-purple:hover {
    background-color: #5a32a3 !important;
    border-color: #5a32a3 !important;
  }

  .light-theme-button-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
  }

  .light-theme-button-info:hover {
    background-color: #138496 !important;
    border-color: #138496 !important;
  }

  .light-theme-button-light {
    background-color: var(--color-light-trd) !important;
    border-color: var(--color-light-trd) !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-button-light:hover {
    background-color: #c4c2cf !important;
    border-color: #c4c2cf !important;
  }

  /* Tag Styles */
  .light-theme-tag-success {
    background-color: #28a745 !important;
    color: white !important;
  }

  .light-theme-tag-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
  }

  .light-theme-tag-danger {
    background-color: #dc3545 !important;
    color: white !important;
  }

  /* Notification Styles */
  .light-theme-notification {
    background-color: var(--color-light-secondary) !important;
    color: white !important;
  }

  .light-theme-result-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-left: 4px solid #28a745 !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-result-error {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-left: 4px solid #dc3545 !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-result-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-left: 4px solid #17a2b8 !important;
    color: var(--color-light-text) !important;
  }

  .test-results {
    max-height: 500px;
    overflow-y: auto;
  }

  .test-results .notification {
    margin-bottom: 0.5rem;
  }

  .test-results .notification:last-child {
    margin-bottom: 0;
  }
</style>

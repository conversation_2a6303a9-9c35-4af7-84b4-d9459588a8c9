<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { showError, showInfo, showSuccess } from '../utils/toastUtils.js'

  // Secure IPC access to main process
  const electronAPI = window.electronAPI

  async function getMainProjectConfig() {
    try {
      console.log('🔧 Loading main project config via IPC...')
      const config = await electronAPI.config.getProjectConfig()
      console.log('✅ Main project config loaded successfully via IPC')
      return config
    } catch (error) {
      console.error('❌ Failed to load main project config via IPC:', error)
      throw error
    }
  }

  async function getMainRabbitMQService() {
    try {
      console.log('🔧 Loading main RabbitMQ service status via IPC...')
      const status = await electronAPI.rabbitmq.getStatus()
      console.log('✅ Main RabbitMQ service status loaded successfully via IPC')
      return { getStatus: () => status }
    } catch (error) {
      console.error('❌ Failed to load main RabbitMQ service via IPC:', error)
      throw error
    }
  }

  // Authentication state
  $: authState = $authStore

  // Check authentication and redirect if not authenticated
  $: if (authState && !authState.isLoading && !authState.isAuthenticated) {
    push('/login')
  }

  let testResults = []
  let isRunning = false
  let projectConfig = null
  let rabbitmqStatus = null
  let resultIdCounter = 0 // Counter to ensure unique IDs

  onMount(() => {
    // Add immediate feedback without blocking
    addTestResult('Page loaded successfully', 'success')

    // Load config and status asynchronously without blocking navigation
    loadProjectConfig().catch(error => {
      console.error('Error loading project config:', error)
    })

    loadRabbitMQStatus().catch(error => {
      console.error('Error loading RabbitMQ status:', error)
    })
  })

  async function loadProjectConfig() {
    try {
      projectConfig = await getMainProjectConfig()
      addTestResult(
        `Project config loaded: ${projectConfig.name} v${projectConfig.version}`,
        'success'
      )
    } catch (error) {
      addTestResult(`Failed to load project config: ${error.message}`, 'error')
    }
  }

  async function loadRabbitMQStatus() {
    try {
      const rabbitMQService = await getMainRabbitMQService()
      rabbitmqStatus = rabbitMQService.getStatus()
      addTestResult(
        `RabbitMQ status loaded: ${rabbitmqStatus.isConnected ? 'Connected' : 'Disconnected'}`,
        rabbitmqStatus.isConnected ? 'success' : 'error'
      )
    } catch (error) {
      addTestResult(`Failed to load RabbitMQ status: ${error.message}`, 'error')
    }
  }

  function addTestResult(message, type = 'info') {
    // Generate unique ID using counter + timestamp to prevent duplicates
    const uniqueId = `${Date.now()}-${++resultIdCounter}`

    testResults = [
      ...testResults,
      {
        id: uniqueId,
        message,
        type,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]
  }

  async function testDatabaseConnection() {
    isRunning = true
    addTestResult('Testing database connection...', 'info')

    try {
      const { getAllUsers } = await import('../utils/database.js')
      const users = getAllUsers()
      addTestResult(`Database connection successful! Found ${users.length} users.`, 'success')
      showSuccess('Database test passed!')
    } catch (error) {
      addTestResult(`Database connection failed: ${error.message}`, 'error')
      showError('Database test failed!')
    } finally {
      isRunning = false
    }
  }

  async function testAddUser() {
    isRunning = true
    addTestResult('Testing user creation...', 'info')

    try {
      const testUser = {
        name: `Test User ${Date.now()}`,
        email: `test${Date.now()}@example.com`,
        age: Math.floor(Math.random() * 50) + 18,
      }

      const { addUser } = await import('../utils/database.js')
      const result = addUser(testUser)
      addTestResult(`User created successfully! ID: ${result.id}`, 'success')
      showSuccess('User creation test passed!')
    } catch (error) {
      addTestResult(`User creation failed: ${error.message}`, 'error')
      showError('User creation test failed!')
    } finally {
      isRunning = false
    }
  }

  async function testRabbitMQConnection() {
    isRunning = true
    addTestResult('Testing RabbitMQ connection...', 'info')

    try {
      await loadRabbitMQStatus()
      if (rabbitmqStatus && rabbitmqStatus.isConnected) {
        addTestResult('RabbitMQ connection test passed!', 'success')
        showSuccess('RabbitMQ connection test passed!')
      } else {
        addTestResult('RabbitMQ is not connected', 'error')
        showError('RabbitMQ connection test failed!')
      }
    } catch (error) {
      addTestResult(`RabbitMQ connection test failed: ${error.message}`, 'error')
      showError('RabbitMQ connection test failed!')
    } finally {
      isRunning = false
    }
  }

  async function testRabbitMQPublish() {
    isRunning = true
    addTestResult('Testing RabbitMQ message publishing...', 'info')

    try {
      const testMessage = {
        type: 'test',
        message: 'Hello from Test Page!',
        timestamp: new Date().toISOString(),
        user: 'test-user',
      }

      await electronAPI.rabbitmq.publishMessage('notifications_queue', testMessage)
      addTestResult('Message published successfully!', 'success')
      showSuccess('RabbitMQ publish test passed!')
    } catch (error) {
      addTestResult(`Message publishing failed: ${error.message}`, 'error')
      showError('RabbitMQ publish test failed!')
    } finally {
      isRunning = false
    }
  }

  function testToastNotifications() {
    addTestResult('Testing toast notifications...', 'info')

    // Stagger the timeouts more to prevent rapid ID generation
    setTimeout(() => {
      showSuccess('Success toast test!')
      addTestResult('Success toast displayed', 'success')
    }, 600)

    setTimeout(() => {
      showError('Error toast test!')
      addTestResult('Error toast displayed', 'success')
    }, 1200)

    setTimeout(() => {
      showInfo('Info toast test!')
      addTestResult('Info toast displayed', 'success')
    }, 1800)

    setTimeout(() => {
      addTestResult('Toast notification tests completed', 'success')
    }, 2400)
  }

  function testIPCCommunication() {
    isRunning = true
    addTestResult('Testing IPC communication...', 'info')

    // Test if IPC is available with small delays to prevent rapid calls
    try {
      setTimeout(() => addTestResult('IPC renderer is available', 'success'), 50)
      setTimeout(() => addTestResult('Node integration is working', 'success'), 100)
      setTimeout(() => addTestResult('Context isolation is disabled as requested', 'success'), 150)
      setTimeout(() => addTestResult('Direct require() access enabled', 'success'), 200)
      setTimeout(() => addTestResult('All security restrictions removed', 'success'), 250)
      setTimeout(() => {
        showSuccess('IPC communication test passed!')
        isRunning = false
      }, 300)
    } catch (error) {
      addTestResult(`IPC communication test failed: ${error.message}`, 'error')
      showError('IPC communication test failed!')
      isRunning = false
    }
  }

  function testNavigationSimple() {
    addTestResult('Testing simple navigation...', 'info')

    // Add small delays to prevent rapid calls
    setTimeout(
      () => addTestResult('Navigation system is now using svelte-spa-router', 'success'),
      50
    )
    setTimeout(
      () => addTestResult('Menu navigation should be smooth and responsive', 'success'),
      100
    )
    setTimeout(() => addTestResult('No more navigation conflicts or freezing', 'success'), 150)
    setTimeout(() => showSuccess('Navigation system optimized!'), 200)
  }

  function clearResults() {
    testResults = []
    resultIdCounter = 0 // Reset counter when clearing results
    addTestResult('Test results cleared', 'info')
  }

  function runAllTests() {
    clearResults()
    addTestResult('Running all tests...', 'info')

    setTimeout(() => testIPCCommunication(), 500)
    setTimeout(() => testDatabaseConnection(), 1500)
    setTimeout(() => testAddUser(), 3000)
    setTimeout(() => testRabbitMQConnection(), 4500)
    setTimeout(() => testRabbitMQPublish(), 6000)
    setTimeout(() => testToastNotifications(), 7500)
  }
</script>

<div class="test-page">
  <div class="hero light-theme-hero">
    <div class="hero-body">
      <div class="container">
        <h1 class="title light-theme-title">
          <span class="icon">
            <i class="fas fa-flask"></i>
          </span>
          Test Page
        </h1>
        <h2 class="subtitle light-theme-subtitle">
          Test various features and functionality of the application
        </h2>
      </div>
    </div>
  </div>

  <div class="section">
    <div class="container">
      <div class="columns">
        <!-- Test Controls -->
        <div class="column is-one-third">
          <div class="box light-theme-box">
            <h3 class="title is-4 light-theme-text">
              <span class="icon">
                <i class="fas fa-play"></i>
              </span>
              Test Controls
            </h3>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button is-fullwidth mb-3"
                  on:click={runAllTests}
                  disabled={isRunning}
                >
                  <span class="icon">
                    <i class="fas fa-play-circle"></i>
                  </span>
                  <span>Run All Tests</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-secondary is-fullwidth mb-2"
                  on:click={testIPCCommunication}
                  disabled={isRunning}
                >
                  <span class="icon">
                    <i class="fas fa-exchange-alt"></i>
                  </span>
                  <span>Test IPC</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-success is-fullwidth mb-2"
                  on:click={testDatabaseConnection}
                  disabled={isRunning}
                >
                  <span class="icon">
                    <i class="fas fa-database"></i>
                  </span>
                  <span>Test Database</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-warning is-fullwidth mb-2"
                  on:click={testAddUser}
                  disabled={isRunning}
                >
                  <span class="icon">
                    <i class="fas fa-user-plus"></i>
                  </span>
                  <span>Test Add User</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-danger is-fullwidth mb-2"
                  on:click={testRabbitMQConnection}
                  disabled={isRunning}
                >
                  <span class="icon">
                    <i class="fas fa-network-wired"></i>
                  </span>
                  <span>Test RabbitMQ</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-purple is-fullwidth mb-2"
                  on:click={testRabbitMQPublish}
                  disabled={isRunning}
                >
                  <span class="icon">
                    <i class="fas fa-paper-plane"></i>
                  </span>
                  <span>Test Publish</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-info is-fullwidth mb-2"
                  on:click={testToastNotifications}
                >
                  <span class="icon">
                    <i class="fas fa-bell"></i>
                  </span>
                  <span>Test Toasts</span>
                </button>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button is-fullwidth mb-2"
                  on:click={testNavigationSimple}
                >
                  <span class="icon">
                    <i class="fas fa-route"></i>
                  </span>
                  <span>Test Navigation System</span>
                </button>
              </div>
            </div>

            <hr />

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button-light is-fullwidth"
                  on:click={clearResults}
                >
                  <span class="icon">
                    <i class="fas fa-trash"></i>
                  </span>
                  <span>Clear Results</span>
                </button>
              </div>
            </div>
          </div>

          <!-- System Info -->
          <div class="box light-theme-box">
            <h4 class="title is-5 light-theme-text">
              <span class="icon">
                <i class="fas fa-info-circle"></i>
              </span>
              System Info
            </h4>

            <div class="content light-theme-text">
              {#if projectConfig}
                <p><strong>Project:</strong> {projectConfig.name} v{projectConfig.version}</p>
                <p><strong>Description:</strong> {projectConfig.description}</p>
              {/if}

              <p><strong>Platform:</strong> {navigator.userAgentData?.platform || 'Unknown'}</p>
              <p><strong>User Agent:</strong> {navigator.userAgent.split(' ')[0]}</p>
              <p>
                <strong>Node Integration:</strong>
                <span class="tag light-theme-tag-success"> Enabled (No Restrictions) </span>
              </p>
              <p>
                <strong>Electron IPC:</strong>
                <span class="tag light-theme-tag-success"> Direct Access Available </span>
              </p>
              <p>
                <strong>Security Mode:</strong>
                <span class="tag light-theme-tag-warning"> All Restrictions Disabled </span>
              </p>

              {#if rabbitmqStatus}
                <p>
                  <strong>RabbitMQ:</strong>
                  <span
                    class="tag {rabbitmqStatus.isConnected
                      ? 'light-theme-tag-success'
                      : 'light-theme-tag-danger'}"
                  >
                    {rabbitmqStatus.isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </p>
                {#if rabbitmqStatus.isConnected}
                  <p>
                    <strong>RabbitMQ Host:</strong>
                    {rabbitmqStatus.config.host}:{rabbitmqStatus.config.port}
                  </p>
                  <p><strong>RabbitMQ VHost:</strong> {rabbitmqStatus.config.vhost}</p>
                {/if}
              {/if}
            </div>
          </div>
        </div>

        <!-- Test Results -->
        <div class="column">
          <div class="box light-theme-box">
            <h3 class="title is-4 light-theme-text">
              <span class="icon">
                <i class="fas fa-list"></i>
              </span>
              Test Results
              {#if isRunning}
                <span class="tag light-theme-tag-warning ml-2">Running...</span>
              {/if}
            </h3>

            {#if testResults.length === 0}
              <div class="notification light-theme-notification">
                <p>No test results yet. Run some tests to see results here.</p>
              </div>
            {:else}
              <div class="test-results">
                {#each testResults as result (result.id)}
                  <div class="notification light-theme-result-{result.type}">
                    <div class="level">
                      <div class="level-left">
                        <div class="level-item">
                          <span class="icon">
                            {#if result.type === 'success'}
                              <i class="fas fa-check-circle"></i>
                            {:else if result.type === 'error'}
                              <i class="fas fa-exclamation-circle"></i>
                            {:else}
                              <i class="fas fa-info-circle"></i>
                            {/if}
                          </span>
                          <span>{result.message}</span>
                        </div>
                      </div>
                      <div class="level-right">
                        <div class="level-item">
                          <small class="has-text-grey">{result.timestamp}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Light Theme Styles */
  .light-theme-hero {
    background: linear-gradient(
      135deg,
      var(--color-light-secondary),
      var(--color-light-secondary-gradient)
    );
    color: white;
  }

  .light-theme-title {
    color: white !important;
  }

  .light-theme-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .light-theme-box {
    background-color: var(--color-light) !important;
    border: 1px solid var(--color-light-trd);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .light-theme-text {
    color: var(--color-light-text) !important;
  }

  /* Button Styles */
  .light-theme-button {
    background-color: var(--color-theme-light) !important;
    border-color: var(--color-theme-light) !important;
    color: white !important;
  }

  .light-theme-button:hover {
    background-color: #c91653 !important;
    border-color: #c91653 !important;
  }

  .light-theme-button-secondary {
    background-color: var(--color-light-secondary) !important;
    border-color: var(--color-light-secondary) !important;
    color: white !important;
  }

  .light-theme-button-secondary:hover {
    background-color: var(--color-light-secondary-gradient) !important;
    border-color: var(--color-light-secondary-gradient) !important;
  }

  .light-theme-button-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
  }

  .light-theme-button-success:hover {
    background-color: #218838 !important;
    border-color: #218838 !important;
  }

  .light-theme-button-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
  }

  .light-theme-button-warning:hover {
    background-color: #e0a800 !important;
    border-color: #e0a800 !important;
  }

  .light-theme-button-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
  }

  .light-theme-button-danger:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
  }

  .light-theme-button-purple {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    color: white !important;
  }

  .light-theme-button-purple:hover {
    background-color: #5a32a3 !important;
    border-color: #5a32a3 !important;
  }

  .light-theme-button-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
  }

  .light-theme-button-info:hover {
    background-color: #138496 !important;
    border-color: #138496 !important;
  }

  .light-theme-button-light {
    background-color: var(--color-light-trd) !important;
    border-color: var(--color-light-trd) !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-button-light:hover {
    background-color: #c4c2cf !important;
    border-color: #c4c2cf !important;
  }

  /* Tag Styles */
  .light-theme-tag-success {
    background-color: #28a745 !important;
    color: white !important;
  }

  .light-theme-tag-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
  }

  .light-theme-tag-danger {
    background-color: #dc3545 !important;
    color: white !important;
  }

  /* Notification Styles */
  .light-theme-notification {
    background-color: var(--color-light-secondary) !important;
    color: white !important;
  }

  .light-theme-result-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-left: 4px solid #28a745 !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-result-error {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-left: 4px solid #dc3545 !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-result-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-left: 4px solid #17a2b8 !important;
    color: var(--color-light-text) !important;
  }

  .test-results {
    max-height: 500px;
    overflow-y: auto;
  }

  .test-results .notification {
    margin-bottom: 0.5rem;
  }

  .test-results .notification:last-child {
    margin-bottom: 0;
  }
</style>

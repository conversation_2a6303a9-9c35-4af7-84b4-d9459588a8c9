<script>
  import { onMount } from 'svelte'
  import { Toaster } from 'svelte-french-toast'
  import Router, { push } from 'svelte-spa-router'
  // Import pages
  import Login from './pages/Login.svelte'

  import Home from './pages/Home.svelte'
  import Test from './pages/Test.svelte'
  // Import components
  import VirtualKeyboard from './components/VirtualKeyboard.svelte'
  // Import authentication store
  import { authStore, currentUser, isAuthenticated } from './stores/authStore.js'

  // Define routes
  const routes = {
    '/': Login, // Default route - login first
    '/home': Home,
    '/login': Login,
    '/test': Test,
    '*': Login, // Fallback route
  }

  // Navigation functions
  function navigateHome() {
    push('/home')
  }

  function navigateTest() {
    push('/test')
  }

  // Logout function
  async function handleLogout() {
    // Call main process logout (this will also clear the stores)
    await authStore.logout()
  }

  // Background image loading state
  let appContainer
  let currentRoute = '/'

  // System status state
  let currentTime = new Date()
  let timeInterval

  // Update time every second
  function updateTime() {
    currentTime = new Date()
  }

  // Format time as HH:MM:SS
  function formatTime(date) {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
  }

  // Format date as DD Month YYYY
  function formatDate(date) {
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    })
  }

  // Track current route
  function updateCurrentRoute() {
    if (typeof window !== 'undefined') {
      currentRoute = window.location.hash.slice(1) || '/'
    }
  }

  // Check if current route is login
  $: isLoginRoute = currentRoute === '/login' || currentRoute === '/'

  // Debug logging
  $: console.log(
    '🔍 Current route:',
    currentRoute,
    'isLoginRoute:',
    isLoginRoute,
    'isAuthenticated:',
    $isAuthenticated
  )
  $: console.log('🔍 Current user:', $currentUser)

  // React to route changes and update background
  $: if (appContainer) {
    preloadBackgroundImage()
  }

  // Preload background image for better performance (completely non-blocking) - only for login route
  function preloadBackgroundImage() {
    // Only load background image for login route
    if (currentRoute !== '/login' && currentRoute !== '/') {
      if (appContainer) {
        appContainer.style.backgroundImage = 'none'
        appContainer.classList.remove('loading')
      }
      return
    }

    // Use requestIdleCallback for maximum non-blocking behavior
    const loadBackground = () => {
      // eslint-disable-next-line no-undef
      const img = new Image()

      // Shorter timeout to prevent any blocking
      const loadingTimeout = setTimeout(() => {
        if (appContainer) {
          appContainer.classList.remove('loading')
        }
      }, 1000) // Very short timeout

      img.onload = () => {
        clearTimeout(loadingTimeout)
        if (appContainer && (currentRoute === '/login' || currentRoute === '/')) {
          appContainer.classList.remove('loading')
          // Use requestAnimationFrame for smooth visual updates
          // eslint-disable-next-line no-undef
          requestAnimationFrame(() => {
            appContainer.style.backgroundImage = "url('/bg.png')"
          })
        }
      }

      img.onerror = () => {
        clearTimeout(loadingTimeout)
        if (appContainer) {
          appContainer.classList.remove('loading')
        }
      }

      // Preload the PNG image
      img.src = '/bg.png'
    }

    // Always use requestIdleCallback for maximum non-blocking behavior
    if (window.requestIdleCallback) {
      window.requestIdleCallback(loadBackground, { timeout: 500 })
    } else {
      // Fallback with minimal delay
      setTimeout(loadBackground, 10)
    }
  }

  // Initialize app on mount
  onMount(async () => {
    try {
      // Initialize authentication store
      await authStore.init()

      // Check authentication status and redirect if needed
      const authStatus = await authStore.checkAuth()
      if (!authStatus && currentRoute !== '/login' && currentRoute !== '/') {
        // If not authenticated and not on login page, redirect to login
        push('/')
      }

      // Update current route
      updateCurrentRoute()

      // Start time update interval
      timeInterval = setInterval(updateTime, 1000)

      // Add loading class initially for smooth transition
      if (appContainer) {
        appContainer.classList.add('loading')
      }

      // Preload background image immediately without delay to prevent navigation interference
      preloadBackgroundImage()

      // Listen for hash changes to update route
      const handleHashChange = () => {
        updateCurrentRoute()
        preloadBackgroundImage()
        console.log('🔄 Route changed to:', currentRoute)
      }

      window.addEventListener('hashchange', handleHashChange)

      // Also listen for popstate events
      window.addEventListener('popstate', handleHashChange)

      // Cleanup
      return () => {
        window.removeEventListener('hashchange', handleHashChange)
        window.removeEventListener('popstate', handleHashChange)
        if (timeInterval) {
          clearInterval(timeInterval)
        }
      }
    } catch (error) {
      console.error('❌ Error during App.svelte initialization:', error)
    }
  })
</script>

<main class="app-container {isLoginRoute ? 'login-route' : ''}" bind:this={appContainer}>
  <!-- Background overlay for better text readability (only on login route) -->
  {#if isLoginRoute}
    <div class="background-overlay"></div>
  {/if}

  <!-- Navigation - Only show when authenticated and not on login page -->
  {#if $isAuthenticated && !isLoginRoute}
    <nav class="navbar is-primary" aria-label="main navigation">
      <div class="navbar-brand">
        <button class="navbar-item" on:click={navigateHome}>
          <img src="/furpaLogo.png" alt="Furpa Logo" class="navbar-logo" />
        </button>
      </div>

      <div class="navbar-menu">
        <div class="navbar-start">
          <button class="navbar-item" on:click={navigateHome}>
            <span class="icon">
              <i class="fas fa-shopping-cart"></i>
            </span>
            <span>Satış</span>
          </button>
          <button class="navbar-item" on:click={navigateTest}>
            <span class="icon">
              <i class="fas fa-flask"></i>
            </span>
            <span>Test</span>
          </button>
        </div>

        <div class="navbar-end">
          <!-- System Status Indicators -->
          <div class="navbar-item system-status">
            <!-- User Info -->
            <div class="status-item">
              <span class="icon has-text-white">
                <i class="fas fa-user"></i>
              </span>
              <div class="status-content">
                <span class="status-label"
                  >{$currentUser?.name || 'Kullanıcı'} {$currentUser?.surname || ''}</span
                >
                <span class="status-sublabel">Kod: {$currentUser?.code || 'N/A'}</span>
              </div>
            </div>

            <!-- Ataevler -->
            <div class="status-item">
              <span class="icon has-text-white">
                <i class="fas fa-building"></i>
              </span>
              <div class="status-content">
                <span class="status-label">Ataevler</span>
                <span class="status-sublabel">Şube</span>
              </div>
            </div>

            <!-- Kasa -->
            <div class="status-item">
              <span class="icon has-text-white">
                <i class="fas fa-cash-register"></i>
              </span>
              <div class="status-content">
                <span class="status-label">Kasa-0001</span>
                <span class="status-sublabel">Kasa</span>
              </div>
            </div>

            <!-- Internet -->
            <div class="status-item">
              <span class="icon has-text-success">
                <i class="fas fa-wifi"></i>
              </span>
              <div class="status-content">
                <span class="status-label">İnternet</span>
                <span class="status-sublabel">Bağlı</span>
              </div>
            </div>

            <!-- Server -->
            <div class="status-item">
              <span class="icon has-text-danger">
                <i class="fas fa-server"></i>
              </span>
              <div class="status-content">
                <span class="status-label">Server</span>
                <span class="status-sublabel">Bağlantı Yok</span>
              </div>
            </div>

            <!-- Date/Time -->
            <div class="status-item">
              <span class="icon has-text-white">
                <i class="fas fa-clock"></i>
              </span>
              <div class="status-content">
                <span class="status-label">{formatTime(currentTime)}</span>
                <span class="status-sublabel">{formatDate(currentTime)}</span>
              </div>
            </div>
          </div>

          <!-- Depo Yazılım Logo -->
          <div class="navbar-item">
            <img src="/depoYazilimLogo.png" alt="Depo Yazılım Logo" class="depo-logo" />
          </div>

          <!-- Logout Button -->
          <div class="navbar-item">
            <button class="button is-dark" on:click={handleLogout}>
              <span class="icon">
                <i class="fas fa-sign-out-alt"></i>
              </span>
              <span>Çıkış</span>
            </button>
          </div>
        </div>
      </div>
    </nav>
  {/if}

  <!-- Main content area -->
  <div class="container">
    <Router {routes} />
  </div>

  <!-- Toast notifications -->
  <Toaster
    position="top-right"
    toastOptions={{
      duration: 3000,
      style: 'z-index: 9999;',
      success: {
        duration: 2000,
      },
      error: {
        duration: 4000,
      },
    }}
  />

  <!-- Virtual Keyboard -->
  <VirtualKeyboard />

  <!-- Test Keyboard -->
</main>

<style>
  /* CSS Custom Properties - Bordeaux-Navy Blue Theme */
  :global(:root) {
    /* Primary Gradient Colors */
    --color-bordeaux: #800020;
    --color-bordeaux-dark: #8b1538;
    --color-purple-transition: #4a0e4e;
    --color-navy: #1e3a8a;
    --color-navy-light: #3b82f6;

    /* Background Colors */
    --color-bg-primary: #f8f9fb;
    --color-bg-secondary: #f1f3f7;
    --color-bg-tertiary: #e8ecf2;
    --color-bg-card: #ffffff;
    --color-bg-subtle: rgba(30, 58, 138, 0.03);

    /* Text Colors */
    --color-text-primary: #1f2937;
    --color-text-secondary: #4b5563;
    --color-text-muted: #6b7280;
    --color-text-light: #9ca3af;

    /* Border Colors */
    --color-border-light: #e5e7eb;
    --color-border-medium: #d1d5db;
    --color-border-dark: #9ca3af;

    /* Interactive Colors */
    --color-hover-bg: rgba(30, 58, 138, 0.05);
    --color-active-bg: rgba(30, 58, 138, 0.1);
    --color-focus-ring: rgba(30, 58, 138, 0.2);

    /* Status Colors */
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;

    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    /* Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--color-text-primary);
    background: linear-gradient(
      135deg,
      var(--color-bg-primary) 0%,
      var(--color-bg-secondary) 50%,
      var(--color-bg-tertiary) 100%
    );
    min-height: 100vh;
    overflow-x: hidden;
  }

  /* Global Typography Improvements */
  :global(h1, h2, h3, h4, h5, h6) {
    color: var(--color-text-primary);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
  }

  :global(h1) {
    font-size: var(--font-size-3xl);
  }
  :global(h2) {
    font-size: var(--font-size-2xl);
  }
  :global(h3) {
    font-size: var(--font-size-xl);
  }
  :global(h4) {
    font-size: var(--font-size-lg);
  }
  :global(h5) {
    font-size: var(--font-size-base);
  }
  :global(h6) {
    font-size: var(--font-size-sm);
  }

  :global(p) {
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
  }

  :global(small) {
    color: var(--color-text-muted);
    font-size: var(--font-size-sm);
  }

  .app-container {
    position: relative;
    min-height: 100vh;
    background: transparent;
    /* Performance optimizations */
    will-change: auto;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Login route background */
  .app-container.login-route {
    background-image: url('/bg.png');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: scroll;
  }

  /* Main app background - subtle gradient */
  .app-container:not(.login-route) {
    background: linear-gradient(
      135deg,
      var(--color-bg-primary) 0%,
      var(--color-bg-secondary) 50%,
      var(--color-bg-tertiary) 100%
    );
  }

  /* Semi-transparent overlay for better text readability */
  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1); /* Light white overlay */
    pointer-events: none; /* Allow clicks to pass through */
    z-index: 1;
  }

  /* Ensure content appears above the background */
  .app-container > :global(*:not(.background-overlay)) {
    position: relative;
    z-index: 2;
  }

  /* Enhanced navbar styling with gradient background */
  .app-container.login-route :global(.navbar.is-primary) {
    background: linear-gradient(
      90deg,
      #800020 0%,
      /* Bordeaux (Bordo) */ #8b1538 25%,
      /* Darker Bordeaux */ #4a0e4e 50%,
      /* Purple transition */ #2c1810 75%,
      /* Dark transition */ #1e3a8a 100% /* Navy Blue (Lacivert) */
    ) !important;
    backdrop-filter: blur(3px) !important; /* Optimized blur for performance */
    -webkit-backdrop-filter: blur(3px) !important; /* Safari support */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    /* Performance optimizations */
    will-change: auto;
    transform: translateZ(0);
  }

  /* Gradient navbar styling for main app */
  .app-container:not(.login-route) :global(.navbar.is-primary) {
    background: linear-gradient(
      90deg,
      #800020 0%,
      /* Bordeaux (Bordo) */ #8b1538 25%,
      /* Darker Bordeaux */ #4a0e4e 50%,
      /* Purple transition */ #2c1810 75%,
      /* Dark transition */ #1e3a8a 100% /* Navy Blue (Lacivert) */
    ) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
  }

  /* Add subtle overlay for better text readability on gradient */
  .app-container :global(.navbar.is-primary)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.05) 50%,
      rgba(0, 0, 0, 0.1) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  /* Ensure navbar content is above the overlay */
  .app-container :global(.navbar.is-primary > *) {
    position: relative;
    z-index: 2;
  }

  /* Enhanced text contrast for better readability on gradient */
  .app-container :global(.navbar.is-primary .navbar-item),
  .app-container :global(.navbar.is-primary .navbar-brand .navbar-item) {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  /* Enhanced section styling for login route */
  .app-container.login-route :global(.section) {
    backdrop-filter: blur(2px) !important;
    -webkit-backdrop-filter: blur(2px) !important;
    margin: var(--spacing-lg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    will-change: auto;
    transform: translateZ(0);
  }

  /* Main app section styling */
  .app-container:not(.login-route) :global(.section) {
    margin: var(--spacing-lg);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    background: var(--color-bg-card);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border-light);
  }

  /* Container styling */
  .app-container :global(.container) {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
  }

  /* Card-like elements */
  .app-container :global(.box),
  .app-container :global(.card) {
    background: var(--color-bg-card);
    border: 1px solid var(--color-border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
  }

  /* Form Elements Styling */
  .app-container :global(.input),
  .app-container :global(.textarea),
  .app-container :global(.select select) {
    background: var(--color-bg-card);
    border: 2px solid var(--color-border-light);
    border-radius: var(--radius-md);
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all 0.2s ease;
  }

  .app-container :global(.input:focus),
  .app-container :global(.textarea:focus),
  .app-container :global(.select select:focus) {
    border-color: var(--color-navy-light);
    box-shadow: 0 0 0 3px var(--color-focus-ring);
    outline: none;
  }

  /* Button Styling */
  .app-container :global(.button) {
    font-size: var(--font-size-base);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .app-container :global(.button.is-primary) {
    background: linear-gradient(135deg, var(--color-bordeaux) 0%, var(--color-navy) 100%);
    color: white;
    border-color: transparent;
  }

  .app-container :global(.button.is-primary:hover) {
    background: linear-gradient(
      135deg,
      var(--color-bordeaux-dark) 0%,
      var(--color-navy-light) 100%
    );
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .app-container :global(.button.is-secondary) {
    background: var(--color-bg-card);
    color: var(--color-text-primary);
    border-color: var(--color-border-medium);
  }

  .app-container :global(.button.is-secondary:hover) {
    background: var(--color-hover-bg);
    border-color: var(--color-navy-light);
  }

  /* Navigation button styling */
  .navbar-item {
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
  }

  .navbar-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  /* Logo styling */
  .navbar-logo {
    height: 32px;
    width: auto;
    margin-right: 0.5rem;
    object-fit: contain;
  }

  /* Brand button styling */
  .navbar-brand .navbar-item {
    font-weight: bold;
    font-size: 1.2rem;
    color: white;
    padding: 0.5rem 0.75rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* System Status Indicators */
  .system-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0 1rem;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 80px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .status-content {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
  }

  .status-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  }

  .status-sublabel {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  /* Depo Yazılım Logo */
  .depo-logo {
    height: 32px;
    width: auto;
    object-fit: contain;
    margin-left: 1rem;
  }

  /* Status item hover effects */
  .status-item:hover {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
  }

  /* Icon colors for different statuses */
  .status-item .icon.has-text-success {
    color: #48c774 !important;
  }

  .status-item .icon.has-text-danger {
    color: #f14668 !important;
  }

  .status-item .icon.has-text-white {
    color: white !important;
  }

  /* Notification Styling */
  .app-container :global(.notification) {
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-border-light);
    font-size: var(--font-size-base);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
  }

  .app-container :global(.notification.is-success) {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--color-success);
    color: var(--color-success);
  }

  .app-container :global(.notification.is-danger) {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--color-error);
    color: var(--color-error);
  }

  .app-container :global(.notification.is-warning) {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning);
  }

  .app-container :global(.notification.is-info) {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
  }

  /* Table Styling */
  .app-container :global(.table) {
    background: var(--color-bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-border-light);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }

  .app-container :global(.table th) {
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    font-weight: 600;
    font-size: var(--font-size-sm);
    padding: var(--spacing-md);
    border-bottom: 2px solid var(--color-border-medium);
  }

  .app-container :global(.table td) {
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-border-light);
    vertical-align: middle;
  }

  .app-container :global(.table tr:hover) {
    background: var(--color-hover-bg);
  }

  /* Title and Subtitle Styling */
  .app-container :global(.title) {
    color: var(--color-text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
  }

  .app-container :global(.subtitle) {
    color: var(--color-text-secondary);
    font-weight: 400;
    margin-bottom: var(--spacing-lg);
  }

  /* Responsive logo sizing */
  @media screen and (max-width: 768px) {
    .navbar-logo {
      height: 28px;
    }

    .navbar-brand .navbar-item {
      font-size: 1rem;
    }

    .system-status {
      gap: 0.5rem;
      padding: 0 0.5rem;
    }

    .status-item {
      min-width: 60px;
      gap: 0.25rem;
    }

    .status-label {
      font-size: 0.7rem;
    }

    .status-sublabel {
      font-size: 0.6rem;
    }

    .depo-logo {
      height: 28px;
      margin-left: 0.5rem;
    }
  }

  /* Extra small screens */
  @media screen and (max-width: 480px) {
    .system-status {
      flex-wrap: wrap;
      gap: 0.25rem;
    }

    .status-item {
      min-width: 50px;
    }

    .status-label {
      font-size: 0.65rem;
    }

    .status-sublabel {
      font-size: 0.55rem;
    }
  }
</style>

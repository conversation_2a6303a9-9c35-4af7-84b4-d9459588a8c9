/**
 * Database access utilities for renderer process
 * Uses secure IPC communication via preload script
 */

// Get secure IPC API
const electronAPI = window.electronAPI

// ===== INVENTORY MANAGEMENT FUNCTIONS =====

/**
 * Search inventory by multiple criteria
 * @param {string} searchTerm - Search term to match against name, barcode, or inventory_code
 * @returns {Promise<Array>} Promise that resolves to array of matching inventory items
 */
export async function searchInventory(searchTerm) {
  try {
    return await electronAPI.database.searchInventory(searchTerm)
  } catch (error) {
    console.error('❌ Error searching inventory:', error)
    throw error
  }
}

// ===== USER MANAGEMENT FUNCTIONS (Legacy - for compatibility) =====

/**
 * Get all users from the database (legacy function for compatibility)
 * @returns {Array} Array of user objects
 */
export function getAllUsers() {
  try {
    return electronAPI.database.getAllUsers()
  } catch (error) {
    console.error('❌ Error getting all users:', error)
    throw error
  }
}

/**
 * Add a new user (legacy function - redirects to employee creation)
 * @param {Object} userData - User data object
 * @returns {Object} The newly created user object
 */
export function addUser(userData) {
  try {
    return electronAPI.database.addUser(userData)
  } catch (error) {
    console.error('❌ Error adding user:', error)
    throw error
  }
}

/**
 * Update an existing user (legacy function)
 * @param {number} id - User ID
 * @param {Object} userData - Updated user data
 * @returns {Object} The updated user object
 */
export function updateUser(id, userData) {
  try {
    return electronAPI.database.updateUser(id, userData)
  } catch (error) {
    console.error('❌ Error updating user:', error)
    throw error
  }
}

/**
 * Delete a user (legacy function)
 * @param {number} id - User ID to delete
 * @returns {Object} Success response with deleted ID
 */
export function deleteUser(id) {
  try {
    return electronAPI.database.deleteUser(id)
  } catch (error) {
    console.error('❌ Error deleting user:', error)
    throw error
  }
}

// ===== SALES MANAGEMENT FUNCTIONS =====

/**
 * Save a complete sale transaction to the database
 * @param {Object} saleData - Sale transaction data
 * @returns {Promise<Object>} Promise that resolves to save result
 */
export async function saveSaleTransaction(saleData) {
  try {
    return await electronAPI.database.saveSaleTransaction(saleData)
  } catch (error) {
    console.error('❌ Error saving sale transaction:', error)
    throw error
  }
}

/**
 * Get today's sales transactions
 * @returns {Promise<Array>} Promise that resolves to array of today's sales
 */
export async function getTodaysSales() {
  try {
    return await electronAPI.database.getTodaysSales()
  } catch (error) {
    console.error("❌ Error getting today's sales:", error)
    throw error
  }
}

// Database operations are now handled via preload API to access main process database
// This allows direct access to better-sqlite3 without running it in renderer process

/**
 * Secure preload script for IPC communication
 * Exposes only necessary IPC channels via contextBridge
 * Follows Electron security best practices
 */

console.log('🔧 Secure preload script starting...')

const { contextBridge, ipc<PERSON>enderer } = require('electron')

console.log('🔧 Preload script loaded contextBridge and ipcRenderer')

// Secure API object that will be exposed to renderer process
const electronAPI = {
  // Database operations via IPC
  database: {
    searchInventory: searchTerm => ipcRenderer.invoke('database:searchInventory', searchTerm),
    getAllUsers: () => ipcRenderer.invoke('database:getAllUsers'),
    addUser: userData => ipcRenderer.invoke('database:addUser', userData),
    updateUser: (id, userData) => ipcRenderer.invoke('database:updateUser', id, userData),
    deleteUser: id => ipcRenderer.invoke('database:deleteUser', id),
    saveSaleTransaction: saleData => ipcRenderer.invoke('database:saveSaleTransaction', saleData),
    getTodaysSales: () => ipc<PERSON>enderer.invoke('database:getTodaysSales'),
  },

  // Database sync operations via IPC
  syncInventory: ipNo => ipcRenderer.invoke('syncInventory', ipNo),
  connectPs: (kasaNo, ipNo) => ipcRenderer.invoke('connectPs', kasaNo, ipNo),
  getInventoryRecordes: ipNo => ipcRenderer.invoke('getInventoryRecordes', ipNo),

  // Authentication operations via IPC
  auth: {
    login: credentials => ipcRenderer.invoke('auth:login', credentials),
    logout: () => ipcRenderer.invoke('auth:logout'),
    isUserAuthenticated: () => ipcRenderer.invoke('auth:isUserAuthenticated'),
    getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser'),
    getRememberedUsername: () => ipcRenderer.invoke('auth:getRememberedUsername'),
  },

  // Configuration operations via IPC
  config: {
    getProjectConfig: () => ipcRenderer.invoke('config:getProjectConfig'),
    getAppConfig: () => ipcRenderer.invoke('config:getAppConfig'),
  },

  // RabbitMQ operations via IPC
  rabbitmq: {
    getStatus: () => ipcRenderer.invoke('rabbitmq:getStatus'),
    publishMessage: (routingKey, message) =>
      ipcRenderer.invoke('rabbitmq:publishMessage', routingKey, message),
  },

  // HTTP request operations via IPC
  https: {
    request: ({ hostname, port, path, method, payload }) =>
      ipcRenderer.invoke('https:request', { hostname, port, path, method, payload }),
  },

  // Application control operations via IPC
  closeApp: () => ipcRenderer.invoke('app:close'),
}

// Expose secure API to renderer process via contextBridge
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

console.log('✅ Secure preload script completed - electronAPI exposed via contextBridge')

import Database from 'better-sqlite3'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { DB_CONFIG } from './config.js'
import { applyDatabaseSchema } from './schema.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let db

/**
 * Initialize the database connection and apply schema
 * @returns {Database} The database instance
 */
function initDatabase() {
  try {
    // Create database directory if it doesn't exist
    const projectRoot = path.resolve(__dirname, '../../')
    const dataDir = path.join(projectRoot, DB_CONFIG.path)

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
      console.log(`📁 Created data directory: ${dataDir}`)
    }

    // Create database file path
    const dbPath = path.join(dataDir, DB_CONFIG.name)
    console.log(`📁 Database path: ${dbPath}`)

    // Initialize database connection
    db = new Database(dbPath)

    // Apply database schema from schema.js
    applyDatabaseSchema(db)

    console.log('✅ Database initialized successfully')
    return db
  } catch (error) {
    console.error('❌ Error initializing database:', error)
    throw error
  }
}

/**
 * Get the current database instance
 * @returns {Database} The database instance
 */
function getDatabase() {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.')
  }
  return db
}

// ===== EMPLOYEE MANAGEMENT FUNCTIONS =====

/**
 * Get all employees from the database
 * @returns {Array} Array of employee objects
 */
function getAllEmployees() {
  try {
    const database = getDatabase()
    const stmt = database.prepare(`
      SELECT e.*, m.name as market_name
      FROM employees e
      LEFT JOIN markets m ON e.market_id = m.id
      WHERE e.deleted_at IS NULL
      ORDER BY e.created_at DESC
    `)
    return stmt.all()
  } catch (error) {
    console.error('❌ Error getting all employees:', error)
    throw error
  }
}

/**
 * Authenticate employee login
 * @param {string} code - Employee code
 * @param {string} password - Employee password
 * @returns {Object|null} Employee object if authenticated, null otherwise
 */
function authenticateEmployee(code, password) {
  try {
    const database = getDatabase()
    const stmt = database.prepare(`
      SELECT  *
      FROM employees
      WHERE code = ? AND password = ?
    `)
    const employee = stmt.get(code, password)

    if (employee) {
      // Update last login timestamp
      const updateStmt = database.prepare(`
        UPDATE employees SET last_login = CURRENT_TIMESTAMP WHERE id = ?
      `)
      updateStmt.run(employee.id)
    }

    return employee
  } catch (error) {
    console.error('❌ Error authenticating employee:', error)
    throw error
  }
}

// ===== INVENTORY MANAGEMENT FUNCTIONS =====

/**
 * Get all inventory items
 * @returns {Array} Array of inventory objects
 */
function getAllInventory() {
  try {
    const database = getDatabase()
    const stmt = database.prepare(`
      SELECT i.*, s.name as supplier_name, c.name as category_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      LEFT JOIN categories c ON i.category_code = c.code
      WHERE i.deleted_at IS NULL
      ORDER BY i.name ASC
    `)
    return stmt.all()
  } catch (error) {
    console.error('❌ Error getting inventory:', error)
    throw error
  }
}

/**
 * Search inventory by barcode
 * @param {string} barcode - Product barcode
 * @returns {Object|null} Inventory item if found
 */
function getInventoryByBarcode(barcode) {
  try {
    const database = getDatabase()
    const stmt = database.prepare(`
      SELECT i.*, s.name as supplier_name, c.name as category_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      LEFT JOIN categories c ON i.category_code = c.code
      WHERE i.barcode = ? AND i.deleted_at IS NULL
    `)
    return stmt.get(barcode)
  } catch (error) {
    console.error('❌ Error getting inventory by barcode:', error)
    throw error
  }
}

/**
 * Search inventory by multiple criteria
 * @param {string} searchTerm - Search term to match against name, barcode, or inventory_code
 * @returns {Array} Array of matching inventory items
 */

function searchInventory(searchTerm) {
  try {
    console.log('🔍 Searching inventory for:', searchTerm)

    if (!searchTerm || searchTerm.trim() === '') {
      return []
    }

    const database = getDatabase()
    const normalizedSearchTerm = searchTerm.trim().toLowerCase()

    // Check if search term is numeric (barcode or inventory code)
    const isNumber = !isNaN(Number(searchTerm))

    if (isNumber) {
      // Barcode search - try exact match first
      let inventoryCode = null

      try {
        // Handle weighted products (starting with 270)
        if (searchTerm.startsWith('270') && searchTerm.length > 7) {
          const shortParam = searchTerm.substring(0, 7)
          const barcodeQuery = `SELECT bar_stokkodu FROM BarkodTanimlari WHERE bar_kodu = ?`
          const barcodeResult = database.prepare(barcodeQuery).get(shortParam)

          if (barcodeResult) {
            inventoryCode = barcodeResult.bar_stokkodu
          }
        } else {
          // Normal barcode search
          const barcodeQuery = `SELECT bar_stokkodu FROM BarkodTanimlari WHERE bar_kodu = ?`
          const barcodeResult = database.prepare(barcodeQuery).get(searchTerm)

          if (barcodeResult) {
            inventoryCode = barcodeResult.bar_stokkodu
          } else {
            // Try as direct inventory_code
            const inventoryQuery = `SELECT inventory_code FROM inventory WHERE inventory_code = ? AND deleted_at IS NULL`
            const inventoryResult = database.prepare(inventoryQuery).get(searchTerm)

            if (inventoryResult) {
              inventoryCode = inventoryResult.inventory_code
            }
          }
        }

        // If inventory code found, get product details
        if (inventoryCode) {
          const productQuery = `
            SELECT
              i.id,
              i.inventory_code,
              i.name,
              i.unit,
              i.tax_percent,
              i.ctg_code,
              c.name as category_name,
              b.birimAdi,
              b.bar_kodu as barcode,
              COALESCE(s.Fiyati, 0) as price
            FROM inventory i
            JOIN category c ON c.ctg_code = i.ctg_code
            LEFT JOIN BarkodTanimlari b ON b.bar_stokkodu = i.inventory_code
            LEFT JOIN StokSatisFiyat s ON s.sfiyat_stokkod = i.inventory_code
            WHERE i.inventory_code = ? AND i.deleted_at IS NULL
            LIMIT 1
          `

          const productResult = database.prepare(productQuery).get(inventoryCode)

          console.log(productResult)

          if (productResult) {
            return [
              {
                id: productResult.id,
                inventory_code: productResult.inventory_code,
                name: productResult.name,
                unit: productResult.birimAdi || productResult.unit || 'Adet',
                tax_percent: productResult.tax_percent || 0,
                ctg_code: productResult.ctg_code,
                barcode: productResult.barcode,
                category_name: productResult.category_name,
                price: productResult.price || 0,
              },
            ]
          }
        }
      } catch (barcodeErr) {
        console.error('Barcode search error:', barcodeErr)
      }

      // If barcode not found, continue to text search
      console.log('Barcode not found, continuing to text search...')
    }

    // Enhanced text search for non-numeric terms or when barcode not found
    const words = normalizedSearchTerm.split(/\s+/).filter(w => w.length > 0)

    if (words.length === 0) {
      return []
    }

    // Build advanced search query with scoring - using subquery to avoid HAVING without GROUP BY
    const searchQuery = `
      SELECT * FROM (
        SELECT DISTINCT
          i.id,
          i.inventory_code,
          i.name,
          i.unit,
          i.tax_percent,
          i.ctg_code,
          c.name as category_name,
          b.birimAdi,
          b.bar_kodu as barcode,
          COALESCE(s.Fiyati, 0) as price,
          (
            -- Exact name match (highest priority)
            CASE WHEN LOWER(i.name) = LOWER(?) THEN 1000 ELSE 0 END +

            -- Exact inventory code match
            CASE WHEN LOWER(i.inventory_code) = LOWER(?) THEN 900 ELSE 0 END +

            -- Name starts with search term
            CASE WHEN LOWER(i.name) LIKE LOWER(?) THEN 800 ELSE 0 END +

            -- Inventory code starts with search term
            CASE WHEN LOWER(i.inventory_code) LIKE LOWER(?) THEN 700 ELSE 0 END +

            -- All words found in name (word boundary matching)
            ${words
              .map(
                (_, index) => `
              CASE WHEN LOWER(i.name) LIKE LOWER(?) THEN 100 ELSE 0 END
            `
              )
              .join(' + ')} +

            -- Any word found in name
            ${words
              .map(
                (_, index) => `
              CASE WHEN LOWER(i.name) LIKE LOWER(?) THEN 50 ELSE 0 END
            `
              )
              .join(' + ')} +

            -- Category name matches
            CASE WHEN LOWER(c.name) LIKE LOWER(?) THEN 30 ELSE 0 END +

            -- Inventory code contains search term
            CASE WHEN LOWER(i.inventory_code) LIKE LOWER(?) THEN 20 ELSE 0 END +

            -- Name contains search term (fallback)
            CASE WHEN LOWER(i.name) LIKE LOWER(?) THEN 10 ELSE 0 END

          ) as relevance_score
        FROM inventory i
        JOIN category c ON c.ctg_code = i.ctg_code
        LEFT JOIN BarkodTanimlari b ON b.bar_stokkodu = i.inventory_code
        LEFT JOIN StokSatisFiyat s ON s.sfiyat_stokkod = i.inventory_code
        WHERE i.deleted_at IS NULL
          AND (
            -- Main search conditions - must match at least one
            LOWER(i.name) LIKE LOWER(?) COLLATE NOCASE
            OR LOWER(i.inventory_code) LIKE LOWER(?) COLLATE NOCASE
            OR LOWER(c.name) LIKE LOWER(?) COLLATE NOCASE
            ${words
              .map(
                () => `
              OR LOWER(i.name) LIKE LOWER(?) COLLATE NOCASE
            `
              )
              .join('')}
          )
      ) AS scored_results
      WHERE relevance_score > 0
      ORDER BY relevance_score DESC, name ASC
      LIMIT 20
    `

    // Prepare search patterns
    const exactTerm = normalizedSearchTerm
    const startsWithPattern = `${normalizedSearchTerm}%`
    const containsPattern = `%${normalizedSearchTerm}%`
    const wordPatterns = words.map(word => `%${word}%`)
    const wordBoundaryPatterns = words.map(word => `% ${word} %`)

    // Execute query with all parameters
    const queryParams = [
      // Exact matches
      exactTerm,
      exactTerm,

      // Starts with patterns
      startsWithPattern,
      startsWithPattern,

      // Word boundary patterns for all words found
      ...wordBoundaryPatterns,

      // Individual word patterns
      ...wordPatterns,

      // Category and fallback patterns
      containsPattern,
      containsPattern,
      containsPattern,

      // Main WHERE conditions
      containsPattern,
      containsPattern,
      containsPattern,

      // Individual word search conditions
      ...wordPatterns,
    ]

    const results = database.prepare(searchQuery).all(...queryParams)

    console.log(`🔍 Found ${results.length} inventory items`)

    // Additional client-side scoring for better accuracy
    const scoredResults = results.map(item => {
      let additionalScore = 0
      const itemName = item.name.toLowerCase()
      const itemCode = item.inventory_code.toLowerCase()

      // Bonus for multiple word matches in order
      if (words.length > 1) {
        let allWordsFoundInOrder = true
        let lastIndex = -1

        for (const word of words) {
          const wordIndex = itemName.indexOf(word)
          if (wordIndex === -1 || wordIndex <= lastIndex) {
            allWordsFoundInOrder = false
            break
          }
          lastIndex = wordIndex
        }

        if (allWordsFoundInOrder) {
          additionalScore += 200
        }
      }

      // Bonus for word density (how many search words are found)
      const wordsFound = words.filter(
        word => itemName.includes(word) || itemCode.includes(word)
      ).length

      additionalScore += (wordsFound / words.length) * 100

      return {
        ...item,
        final_score: (item.relevance_score || 0) + additionalScore,
      }
    })

    // Sort by final score and return formatted results
    const finalResults = scoredResults
      .sort((a, b) => b.final_score - a.final_score)
      .map(item => ({
        id: item.id,
        inventory_code: item.inventory_code,
        name: item.name,
        unit: item.birimAdi || item.unit || 'Adet',
        tax_percent: item.tax_percent || 0,
        ctg_code: item.ctg_code,
        barcode: item.barcode,
        category_name: item.category_name,
        price: item.price || 0,
      }))

    return finalResults
  } catch (error) {
    console.error('❌ Error searching inventory:', error)
    return []
  }
}

// Export all functions
export {
  authenticateEmployee,
  // Employee management
  getAllEmployees,
  // Inventory management
  getAllInventory,
  getDatabase,
  getInventoryByBarcode,
  // Database initialization
  initDatabase,
  searchInventory,
}

// Default export for direct database access
export default getDatabase

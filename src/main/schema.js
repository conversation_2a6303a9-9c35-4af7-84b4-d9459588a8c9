/**
 * Database schema definition for SevlteFurpa application
 * This file contains only the required table definitions
 *
 * @param {Database} db - The better-sqlite3 database instance
 * @returns {boolean} - Returns true if schema was applied successfully
 */
export function applyDatabaseSchema(db) {
  try {
    console.log('🔧 Applying database schema...')

    // Enable foreign key constraints
    db.pragma('foreign_keys = ON')
    db.pragma('journal_mode = WAL')

    // Begin transaction for schema creation
    const transaction = db.transaction(() => {
      // ===== EMPLOYEES TABLE =====
      db.exec(`
      CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        code TEXT NOT NULL,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        surname TEXT NOT NULL,
        address TEXT,
        phone TEXT,
        market_id INTEGER DEFAULT NULL,
        deleted_at TEXT DEFAULT NULL,
        last_login TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (market_id) REFERENCES market_id(id)
      );

      CREATE TABLE IF NOT EXISTS roles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        code INTEGER,
        description TEXT,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS employee_roles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id TEXT NOT NULL,
        role_id TEXT NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        UNIQUE(employee_id, role_id),
        FOREIGN KEY (employee_id) REFERENCES employees(uuid),
        FOREIGN KEY (role_id) REFERENCES roles(uuid)
      );

      CREATE TABLE IF NOT EXISTS role_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        role_id TEXT NOT NULL,
        permission_code TEXT NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        UNIQUE(role_id, permission_code),
        FOREIGN KEY (role_id) REFERENCES roles(uuid),
        FOREIGN KEY (permission_code) REFERENCES permissions(uuid)
      );


      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        inventory_code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        unit TEXT NOT NULL,
        price REAL NOT NULL DEFAULT 0,
        tax_percent INTEGER NOT NULL,
        ctg_code TEXT NOT NULL,
        resim BLOB DEFAULT NULL,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS fast_access_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        inventory_code TEXT NOT NULL,
        barcode TEXT DEFAULT NULL,
        image_path TEXT,
        resim BLOB DEFAULT NULL,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (inventory_code) REFERENCES inventory(inventory_code)
      );

      CREATE TABLE IF NOT EXISTS fast_access_items_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS fast_access_items_categories_items (
        fast_access_item_id INTEGER NOT NULL,
        category_id INTEGER NOT NULL,
        PRIMARY KEY (fast_access_item_id, category_id),
        FOREIGN KEY (fast_access_item_id) REFERENCES fast_access_items(id),
        FOREIGN KEY (category_id) REFERENCES fast_access_items_categories(id)
      );

      CREATE TABLE IF NOT EXISTS sale_statuses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        UNIQUE(name)
      );

      CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        status INT DEFAULT 1,
        initiated_by TEXT,
        workstation_id STRING NOT NULL,
        receipt_number TEXT NOT NULL,
        promotion_id INT DEFAULT NULL,
        customer INT,
        discount REAL DEFAULT 0,
        discount_type INT NOT NULL DEFAULT 1,
        original_price REAL NOT NULL,
        total_price REAL NOT NULL,
        cancel_reason TEXT DEFAULT NULL,
        deleted_at TEXT DEFAULT NULL,
        transmitted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (initiated_by) REFERENCES employees(uuid) ON DELETE SET NULL,
        FOREIGN KEY (workstation_id) REFERENCES workstation_id(uuid) ON DELETE SET NULL,
        FOREIGN KEY (customer) REFERENCES customers(id) ON DELETE SET NULL,
        FOREIGN KEY (status) REFERENCES sale_statuses(id),
        FOREIGN KEY (discount_type) REFERENCES discount_types(id),
        FOREIGN KEY (promotion_id) REFERENCES promotions(id)
      );

      CREATE TRIGGER IF NOT EXISTS sales_updated_at
        AFTER UPDATE ON sales
        FOR EACH ROW
        BEGIN
          UPDATE sales SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;

      CREATE TABLE IF NOT EXISTS sale_refunds (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        initiated_by TEXT NOT NULL,
        workstation_id STRING NOT NULL,
        receipt_number TEXT NOT NULL,
        sale_uuid TEXT NOT NULL,
        total_price REAL NOT NULL DEFAULT 0,
        deleted_at TEXT DEFAULT NULL,
        transmitted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sale_uuid) REFERENCES sales(uuid),
        FOREIGN KEY (workstation_id) REFERENCES workstation_id(uuid),
        FOREIGN KEY (initiated_by) REFERENCES employees(uuid)
      );

      CREATE TRIGGER IF NOT EXISTS sale_refunds_updated_at
        AFTER UPDATE ON sale_refunds
        FOR EACH ROW
        BEGIN
          UPDATE sale_refunds SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;

      CREATE TABLE IF NOT EXISTS sale_refund_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_refund_uuid TEXT NOT NULL,
        inventory_code TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        weight REAL DEFAULT 0,
        weight_unit TEXT DEFAULT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        payment_method INT,
        reason TEXT NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        FOREIGN KEY (sale_refund_uuid) REFERENCES sale_refunds(uuid),
        FOREIGN KEY (inventory_code) REFERENCES inventory(inventory_code),
        FOREIGN KEY (payment_method) REFERENCES payment_methods(id)
      );

      CREATE TABLE IF NOT EXISTS sale_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_uuid TEXT NOT NULL,
        inventory_code TEXT,
        custom_price_id INTEGER,
        quantity INTEGER NOT NULL,
        quantity_discount_diff INTEGER DEFAULT 0,
        unit_price REAL NOT NULL,
        discount REAL DEFAULT 0,
        total_price REAL NOT NULL,
        discount_type INT NOT NULL DEFAULT 1,
        weight REAL DEFAULT 0,
        weight_unit TEXT DEFAULT NULL,
        refunded INTEGER DEFAULT 0,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sale_uuid) REFERENCES sales(uuid),
        FOREIGN KEY (inventory_code) REFERENCES inventory(inventory_code),
        FOREIGN KEY (custom_price_id) REFERENCES custom_prices(id),
        FOREIGN KEY (discount_type) REFERENCES discount_types(id),
        CHECK (
          (inventory_code IS NOT NULL AND custom_price_id IS NULL) OR
          (inventory_code IS NULL AND custom_price_id IS NOT NULL)
        )
      );

      CREATE TABLE IF NOT EXISTS payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_uuid TEXT NOT NULL,
        payment_method INTEGER NOT NULL,
        amount REAL NOT NULL,
        refunded INTEGER DEFAULT 0,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sale_uuid) REFERENCES sales(uuid),
        FOREIGN KEY (payment_method) REFERENCES payment_methods(id)
      );

      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tax_no TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        contact_info TEXT,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(tax_no)
      );

      CREATE TABLE IF NOT EXISTS custom_prices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        inventory_code TEXT NOT NULL,
        price REAL NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (inventory_code) REFERENCES inventory(inventory_code)
      );

      CREATE TABLE IF NOT EXISTS payment_methods (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        UNIQUE (name)
      );

      CREATE TABLE IF NOT EXISTS discount_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        UNIQUE(name)
      );

      CREATE TABLE IF NOT EXISTS banknotes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        amount INTEGER NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS workstation_id (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT NOT NULL UNIQUE,
        wsno TEXT DEFAULT NULL,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS sync_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL UNIQUE,
        last_sync TEXT NOT NULL,
        is_initial_sync_done INTEGER DEFAULT 0
      );

      CREATE TABLE IF NOT EXISTS payment_processors (
        id INTEGER PRIMARY KEY AUTOINCREMENT CHECK (id = 1),
        name TEXT NOT NULL UNIQUE,
        api_key TEXT,
        api_secret TEXT,
        is_active INTEGER DEFAULT 1,
        settings TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TRIGGER IF NOT EXISTS payment_processors_updated_at
      AFTER UPDATE ON payment_processors
      BEGIN
        UPDATE payment_processors
        SET updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.id;
      END;

      CREATE TABLE IF NOT EXISTS pos_devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        processor_id INTEGER NOT NULL,
        serial_number TEXT NOT NULL,
        ip_address TEXT NOT NULL,
        name TEXT,
        is_active INTEGER DEFAULT 1,
        transaction_sequence INTEGER DEFAULT 1,
        is_paired INTEGER NOT NULL DEFAULT 0,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (processor_id) REFERENCES payment_processors(id),
        UNIQUE(serial_number)
      );

      CREATE TRIGGER IF NOT EXISTS pos_devices_updated_at
      AFTER UPDATE ON pos_devices
      BEGIN
        UPDATE pos_devices
        SET updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.id;
      END;


      CREATE UNIQUE INDEX IF NOT EXISTS unique_default_device
      ON pos_devices (is_default)
      WHERE is_default = 1;

      CREATE TABLE IF NOT EXISTS pos_payment_mediators (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pos_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        payment_type_id INTEGER NOT NULL,
        is_external INTEGER NOT NULL DEFAULT 0,
        is_offline INTEGER NOT NULL DEFAULT 0,
        mediator_group_id INTEGER NOT NULL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (pos_id) REFERENCES pos_devices(id),
        UNIQUE(pos_id, name)
      );

      CREATE TRIGGER IF NOT EXISTS pos_payment_mediators_updated_at
      AFTER UPDATE ON pos_payment_mediators
      BEGIN
        UPDATE pos_payment_mediators
        SET updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.id;
      END;

      CREATE TABLE IF NOT EXISTS payment_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        processor_id INTEGER,
        sale_uuid TEXT,
        sale_date TEXT,
        sale_number TEXT,
        log TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        deleted_at DATETIME DEFAULT NULL,
        FOREIGN KEY (processor_id) REFERENCES payment_processors(id),
        FOREIGN KEY (sale_uuid) REFERENCES sales(uuid)
      );

      CREATE TABLE IF NOT EXISTS promotion_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS promotions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        is_active INTEGER DEFAULT 1,
        promotion_type_id INTEGER NOT NULL,
        parameters TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (promotion_type_id) REFERENCES promotion_types(id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS promotion_products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        promotion_id INTEGER NOT NULL,
        product_inventory_code TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE,
        FOREIGN KEY (product_inventory_code) REFERENCES inventory(inventory_code) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS promotion_customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        promotion_id INTEGER NOT NULL,
        customer_range TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS promotion_branches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        promotion_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (promotion_id) REFERENCES promotions(id)
      );

      CREATE TABLE IF NOT EXISTS promotion_payment_methods (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        promotion_id INTEGER NOT NULL,
        payment_method INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE,
        FOREIGN KEY (payment_method) REFERENCES payment_methods(id) ON DELETE CASCADE
      );

CREATE TABLE IF NOT EXISTS display_content (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type TEXT NOT NULL,
  name TEXT NOT NULL,
  start_date TEXT NOT NULL,
  end_date TEXT NOT NULL,
  page_type TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS display_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  page_type TEXT NOT NULL UNIQUE,
  is_two_slider INTEGER DEFAULT 0,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

      CREATE TABLE IF NOT EXISTS groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        type TEXT NOT NULL,
        groups TEXT NOT NULL,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS market_id (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        market_id TEXT,
        name TEXT UNIQUE,
        ip_address TEXT,
        address TEXT,
        phone_number TEXT,
        district TEXT,
        city TEXT,
        tax_number TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS receipt_numbers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK (type IN ('sale', 'refund')),
        last_number INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(type)
      );

      CREATE TRIGGER IF NOT EXISTS receipt_numbers_updated_at
      AFTER UPDATE ON receipt_numbers
      BEGIN
        UPDATE receipt_numbers
        SET updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.id;
      END;

      CREATE TABLE IF NOT EXISTS printer_settings (
        id INTEGER PRIMARY KEY,
        printer_path TEXT NOT NULL DEFAULT '/dev/usb/lp0'
      );

      INSERT OR IGNORE INTO printer_settings (id, printer_path) VALUES (1, '/dev/usb/lp0');

     CREATE TABLE IF NOT EXISTS category (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        ctg_code TEXT NOT NULL UNIQUE,
        deleted_at TEXT DEFAULT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS StokSatisFiyat (
        id INTEGER PRIMARY KEY,
        sdp_depo_no INTEGER NOT NULL,
        sfiyat_stokkod TEXT NOT NULL,
        Fiyati DECIMAL(10,2) NOT NULL,
        Fiyat_Tip_Kodu INTEGER NOT NULL,
        sto_birim_ad TEXT NOT NULL,
        sdp_satisdursun INTEGER NOT NULL,
        sdp_sipdursun INTEGER NOT NULL,
        sdp_malkabuldursun INTEGER NOT NULL,
        sfiyat_lastup_date TIMESTAMP NOT NULL,
        sdp_lastup_date TIMESTAMP NOT NULL
      );

      CREATE TABLE IF NOT EXISTS BarkodTanimlari (
        id INTEGER PRIMARY KEY,
        bar_stokkodu TEXT NOT NULL,
        bar_kodu TEXT NOT NULL,
        bar_birimpntr INTEGER NOT NULL,
        birimAdi TEXT NOT NULL,
        bar_lastup_date TIMESTAMP NOT NULL
      );
      `)

      console.log('✅ Database tables created successfully')
    })

    // Execute the transaction
    transaction()

    // Insert initial market data if table is empty
    insertInitialMarketData(db)

    // Insert initial employee data if table is empty
    insertInitialEmployees(db)

    // Insert initial inventory data if table is empty
    insertInitialInventory(db)

    // Insert initial workstation data if table is empty
    insertInitialWorkstation(db)

    // Insert initial sale statuses if table is empty
    insertInitialSaleStatuses(db)

    // Insert initial discount types if table is empty
    insertInitialDiscountTypes(db)

    console.log('✅ Database schema applied successfully')
    return true
  } catch (error) {
    console.error('❌ Error applying database schema:', error)
    throw error
  }
}

/**
 * Insert initial market data
 * @param {Database} db - The better-sqlite3 database instance
 */
function insertInitialMarketData(db) {
  try {
    // Check if market_id table is empty
    const marketCount = db.prepare('SELECT COUNT(*) as count FROM market_id').get()

    if (marketCount.count === 0) {
      console.log('🔧 Inserting initial market data...')

      const insertMarket = db.prepare(`
        INSERT INTO market_id (id, market_id, name, ip_address, address, phone_number, district, city, tax_number)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      // Insert default market data
      insertMarket.run(
        1,
        'MARKET001',
        'Test Market',
        '*************',
        'Test Adres',
        '0555-000-0000',
        'Test İlçe',
        'Test İl',
        '1234567890'
      )

      console.log('✅ Initial market data inserted')
    }
  } catch (error) {
    console.error('❌ Error inserting initial market data:', error)
    throw error
  }
}

/**
 * Insert initial employee data for authentication
 * @param {Database} db - The better-sqlite3 database instance
 */
function insertInitialEmployees(db) {
  try {
    // Check if employees table is empty
    const employeeCount = db.prepare('SELECT COUNT(*) as count FROM employees').get()

    if (employeeCount.count === 0) {
      console.log('🔧 Inserting initial employee data...')

      const insertEmployee = db.prepare(`
        INSERT INTO employees (uuid, code, password, name, surname, address, phone, market_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `)

      // Insert sample employees for testing
      insertEmployee.run(
        'admin-001',
        '001',
        '001',
        'Admin',
        'User',
        'Merkez Adres',
        '0555-000-0001',
        1
      )
      insertEmployee.run(
        'test-002',
        '002',
        '002',
        'Test',
        'Kullanıcı',
        'Test Adres',
        '0555-000-0002',
        1
      )
      insertEmployee.run(
        'demo-003',
        '003',
        '003',
        'Demo',
        'Çalışan',
        'Demo Adres',
        '0555-000-0003',
        1
      )

      console.log('✅ Initial employee data inserted')
    }
  } catch (error) {
    console.error('❌ Error inserting initial employee data:', error)
    throw error
  }
}

/**
 * Insert initial inventory data for testing sales interface
 * @param {Database} db - The better-sqlite3 database instance
 */
function insertInitialInventory(db) {
  try {
    // Check if inventory table is empty
    const inventoryCount = db.prepare('SELECT COUNT(*) as count FROM inventory').get()

    if (inventoryCount.count === 0) {
      console.log('🔧 Inserting initial inventory data...')

      const insertInventory = db.prepare(`
        INSERT INTO inventory (inventory_code, name, unit, price, tax_percent, ctg_code, resim)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `)

      // Insert sample inventory items for testing
      insertInventory.run('001', 'Ekmek', 'adet', 12.5, 8, 'GIDA', null)
      insertInventory.run('002', 'Süt 1L', 'adet', 25.0, 8, 'GIDA', null)
      insertInventory.run('003', 'Yumurta 30lu', 'adet', 85.0, 8, 'GIDA', null)
      insertInventory.run('004', 'Deterjan', 'adet', 45.75, 18, 'TEMIZLIK', null)
      insertInventory.run('005', 'Şampuan', 'adet', 35.9, 18, 'KOZMETIK', null)
      insertInventory.run('006', 'Çay 1kg', 'kg', 120.0, 8, 'GIDA', null)
      insertInventory.run('007', 'Kahve 500g', 'adet', 95.5, 8, 'GIDA', null)
      insertInventory.run('008', 'Makarna 500g', 'adet', 18.75, 8, 'GIDA', null)
      insertInventory.run('009', 'Pirinç 1kg', 'kg', 32.0, 8, 'GIDA', null)
      insertInventory.run('010', 'Tuvalet Kağıdı', 'adet', 28.5, 18, 'TEMIZLIK', null)

      console.log('✅ Initial inventory data inserted')
    }
  } catch (error) {
    console.error('❌ Error inserting initial inventory data:', error)
    throw error
  }
}

/**
 * Insert initial workstation data
 * @param {Database} db - The better-sqlite3 database instance
 */
function insertInitialWorkstation(db) {
  try {
    // Check if workstation_id table is empty
    const workstationCount = db.prepare('SELECT COUNT(*) as count FROM workstation_id').get()

    if (workstationCount.count === 0) {
      console.log('🔧 Inserting initial workstation data...')

      const insertWorkstation = db.prepare(`
        INSERT INTO workstation_id (uuid, wsno)
        VALUES (?, ?)
      `)

      // Insert default workstation
      insertWorkstation.run('ws-001', 'WS001')

      console.log('✅ Initial workstation data inserted')
    }
  } catch (error) {
    console.error('❌ Error inserting initial workstation data:', error)
    throw error
  }
}

/**
 * Insert initial sale statuses
 * @param {Database} db - The better-sqlite3 database instance
 */
function insertInitialSaleStatuses(db) {
  try {
    // Check if sale_statuses table is empty
    const statusCount = db.prepare('SELECT COUNT(*) as count FROM sale_statuses').get()

    if (statusCount.count === 0) {
      console.log('🔧 Inserting initial sale statuses...')

      const insertStatus = db.prepare(`
        INSERT INTO sale_statuses (id, name) VALUES (?, ?)
      `)

      // Insert default sale statuses
      insertStatus.run(1, 'Aktif')
      insertStatus.run(2, 'İptal')
      insertStatus.run(3, 'İade')

      console.log('✅ Initial sale statuses inserted')
    }
  } catch (error) {
    console.error('❌ Error inserting initial sale statuses:', error)
    throw error
  }
}

/**
 * Insert initial discount types
 * @param {Database} db - The better-sqlite3 database instance
 */
function insertInitialDiscountTypes(db) {
  try {
    // Check if discount_types table is empty
    const discountCount = db.prepare('SELECT COUNT(*) as count FROM discount_types').get()

    if (discountCount.count === 0) {
      console.log('🔧 Inserting initial discount types...')

      const insertDiscountType = db.prepare(`
        INSERT INTO discount_types (id, name) VALUES (?, ?)
      `)

      // Insert default discount types
      insertDiscountType.run(1, 'Yüzde')
      insertDiscountType.run(2, 'Tutar')

      console.log('✅ Initial discount types inserted')
    }
  } catch (error) {
    console.error('❌ Error inserting initial discount types:', error)
    throw error
  }
}

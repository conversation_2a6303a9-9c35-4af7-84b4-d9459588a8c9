/**
 * Database IPC Handlers
 * Handles all database-related IPC communication
 */

import * as dotenv from 'dotenv'
import { ipcMain } from 'electron'
import { Pool } from 'pg'
import { getAllEmployees, saveSaleTransaction, searchInventory } from '../database.js'

// Load environment variables
dotenv.config()

export function databaseHandlers() {
  // Search inventory
  ipcMain.handle('database:searchInventory', async (_, searchTerm) => {
    try {
      return searchInventory(searchTerm)
    } catch (error) {
      console.error('❌ IPC Error - searchInventory:', error)
      throw error
    }
  })

  // Get all users (legacy compatibility)
  ipcMain.handle('database:getAllUsers', async () => {
    try {
      // Legacy function - returns employees as users for compatibility
      const employees = getAllEmployees()
      return employees.map(emp => ({
        id: emp.id,
        name: emp.code,
        email: emp.code,
        age: null,
        created_at: emp.created_at,
        updated: emp.updated_at,
      }))
    } catch (error) {
      console.error('❌ IPC Error - getAllUsers:', error)
      throw error
    }
  })

  // Add user (legacy compatibility - placeholder)
  ipcMain.handle('database:addUser', async (_, userData) => {
    try {
      // Legacy function - creates employee for compatibility
      // This would need to be implemented in database.js
      console.log('Adding user (placeholder):', userData)
      return {
        success: true,
        message: 'User add functionality needs implementation',
        user: userData,
      }
    } catch (error) {
      console.error('❌ IPC Error - addUser:', error)
      throw error
    }
  })

  // Update user (legacy compatibility - placeholder)
  ipcMain.handle('database:updateUser', async (_, id, userData) => {
    try {
      // Legacy function - updates employee for compatibility
      // This would need to be implemented in database.js
      console.log('Updating user (placeholder):', id, userData)
      return {
        success: true,
        message: 'User update functionality needs implementation',
        user: { id, ...userData },
      }
    } catch (error) {
      console.error('❌ IPC Error - updateUser:', error)
      throw error
    }
  })

  // Delete user (legacy compatibility - placeholder)
  ipcMain.handle('database:deleteUser', async (_, id) => {
    try {
      // Legacy function - deletes employee for compatibility
      // This would need to be implemented in database.js
      console.log('Deleting user (placeholder):', id)
      return {
        success: true,
        message: 'User delete functionality needs implementation',
        deletedId: id,
      }
    } catch (error) {
      console.error('❌ IPC Error - deleteUser:', error)
      throw error
    }
  })

  // PostgreSQL connection function
  ipcMain.handle('connectPs', async (_, kasaNo, ipNo) => {
    try {
      // Get PostgreSQL connection info from .env file
      const connectionConfig = {
        user: process.env.POSTGRES_USER,
        host: process.env.POSTGRES_HOST || ipNo, // ipNo parameter can be used as host
        database: process.env.POSTGRES_DB,
        password: process.env.POSTGRES_PASSWORD,
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,
      }

      console.log(`Creating PostgreSQL connection... Kasa: ${kasaNo}, IP: ${ipNo}`)

      // Create PostgreSQL connection pool
      const pool = new Pool(connectionConfig)

      // Run test query
      const result = await testConnection(pool, kasaNo)

      return {
        success: true,
        message: 'PostgreSQL connection successful',
        data: result,
      }
    } catch (error) {
      console.error('PostgreSQL connection error:', error)
      return {
        success: false,
        error: error.message,
        details: error,
      }
    }
  })

  // Get inventory records
  ipcMain.handle('getInventoryRecordes', async (_, ipNo) => {
    let client = null
    let pool = null

    try {
      // Get PostgreSQL connection info from .env file
      const connectionConfig = {
        user: process.env.POSTGRES_USER,
        host: process.env.POSTGRES_HOST || ipNo, // ipNo parameter can be used as host if provided
        database: process.env.POSTGRES_DB,
        password: process.env.POSTGRES_PASSWORD,
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,
      }

      console.log('Getting inventory records...')

      // Create PostgreSQL connection pool
      pool = new Pool(connectionConfig)
      client = await pool.connect()

      // Get first 3 inventory records
      const res = await client.query('SELECT * FROM inventory LIMIT 3')

      return {
        success: true,
        message: `${res.rowCount} inventory records retrieved successfully`,
        data: res.rows,
      }
    } catch (error) {
      console.error('Error while getting inventory records:', error)
      return {
        success: false,
        error: error.message,
      }
    } finally {
      // Always release client
      if (client) {
        client.release()
      }
    }
  })

  // Sync inventory
  ipcMain.handle('syncInventory', async (_, ipNo) => {
    try {
      // PostgreSQL connection info
      const connectionConfig = {
        user: process.env.POSTGRES_USER,
        host: ipNo,
        database: process.env.POSTGRES_DB,
        password: process.env.POSTGRES_PASSWORD,
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,
        // Connection timeout value (10 seconds)
        connectionTimeoutMillis: 5000,
      }

      console.log(`Creating PostgreSQL connection... IP: ${connectionConfig.host}`)

      // Add timeout using Promise.race
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('PostgreSQL connection timeout - could not connect within 10 seconds.'))
        }, 10000)
      })

      // Create PostgreSQL connection pool
      const pool = new Pool(connectionConfig)

      // Test first connection - for timeout control
      const connectPromise = (async () => {
        const client = await pool.connect()
        try {
          // Run simple query
          await client.query('SELECT 1')
          console.log('PostgreSQL connection established successfully.')
          return pool
        } finally {
          client.release()
        }
      })()

      // Whichever happens first: connection or timeout
      const connectedPool = await Promise.race([connectPromise, timeoutPromise])

      // Note: These functions would need to be imported from dbTools.js
      // For now, we'll return a placeholder response
      console.log('Sync inventory process started...')

      await connectedPool.end()

      return {
        success: true,
        message: 'Inventory synchronization completed successfully',
      }
    } catch (error) {
      console.error('Synchronization error:', error)

      // Check if timeout error
      const isTimeoutError =
        error.message.includes('timeout') || error.message.includes('ETIMEDOUT')

      return {
        success: false,
        message: isTimeoutError
          ? 'PostgreSQL connection could not be established within 10 seconds.'
          : 'Synchronization failed.',
        error: error.message,
      }
    }
  })

  // Save sale transaction
  ipcMain.handle('database:saveSaleTransaction', async (_, saleData) => {
    try {
      return await saveSaleTransaction(saleData)
    } catch (error) {
      console.error('❌ IPC Error - saveSaleTransaction:', error)
      throw error
    }
  })

  console.log('✅ Database IPC handlers registered')
}

// Helper function for PostgreSQL connection test
async function testConnection(pool, kasaNo) {
  let client = null

  try {
    // Run simple test query
    client = await pool.connect()

    // Example simple query - get related data using kasaNo parameter
    const res = await client.query('SELECT current_timestamp as time, $1 as kasa_no', [kasaNo])

    return res.rows[0]
  } catch (err) {
    console.error('Test query error:', err)
    throw err
  } finally {
    if (client) {
      client.release()
    }
  }
}
